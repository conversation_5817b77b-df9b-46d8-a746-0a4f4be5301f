import { PageBreadcrumbs } from '@/features/dashboard/components/PageBreadcrumbs';
import { Card } from '@/components/ui/card';
import { Package, AlertCircle } from 'lucide-react';

export default async function InventoryPage() {
  return (
    <div className="container mx-auto py-8">
      <PageBreadcrumbs
        items={[
          { label: 'Dashboard', href: '/dashboard', type: 'link' },
          { label: 'Platform', href: '/dashboard/platform', type: 'link' },
          { label: 'Inventory', type: 'page' }
        ]}
      />

      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
          Inventory
        </h1>
        <p className="mt-2 text-gray-600 dark:text-gray-400">
          Track stock levels, manage warehouses, and monitor inventory.
        </p>
      </div>

      <Card className="p-12">
        <div className="text-center">
          <div className="w-16 h-16 bg-teal-100 dark:bg-teal-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <Package className="w-8 h-8 text-teal-600" />
          </div>
          <h2 className="text-xl font-semibold mb-2">Inventory Management</h2>
          <p className="text-gray-600 dark:text-gray-400 max-w-md mx-auto">
            The inventory management system is being developed. This will allow you to track stock levels, 
            manage multiple warehouses, set reorder points, and monitor inventory movements across your products.
          </p>
          <div className="mt-6 flex items-center justify-center gap-2 text-sm text-amber-600">
            <AlertCircle className="w-4 h-4" />
            <span>Coming soon</span>
          </div>
        </div>
      </Card>
    </div>
  );
}