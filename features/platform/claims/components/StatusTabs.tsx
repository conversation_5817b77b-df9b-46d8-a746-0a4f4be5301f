'use client';

import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ClaimStatusCounts } from '../lib/types';

interface StatusTabsProps {
  counts: ClaimStatusCounts;
  currentStatus: string;
  onStatusChange: (status: string) => void;
}

export function StatusTabs({ counts, currentStatus, onStatusChange }: StatusTabsProps) {
  return (
    <Tabs value={currentStatus} onValueChange={onStatusChange}>
      <TabsList>
        <TabsTrigger value="all">
          All Claims
          <span className="ml-2 text-xs bg-gray-100 dark:bg-gray-800 px-2 py-0.5 rounded-full">
            {counts.all}
          </span>
        </TabsTrigger>
        <TabsTrigger value="refund">
          Refund
          <span className="ml-2 text-xs bg-gray-100 dark:bg-gray-800 px-2 py-0.5 rounded-full">
            {counts.refund}
          </span>
        </TabsTrigger>
        <TabsTrigger value="replace">
          Replace
          <span className="ml-2 text-xs bg-gray-100 dark:bg-gray-800 px-2 py-0.5 rounded-full">
            {counts.replace}
          </span>
        </TabsTrigger>
        <TabsTrigger value="notRefunded">
          Not Refunded
          <span className="ml-2 text-xs bg-gray-100 dark:bg-gray-800 px-2 py-0.5 rounded-full">
            {counts.notRefunded}
          </span>
        </TabsTrigger>
        <TabsTrigger value="refunded">
          Refunded
          <span className="ml-2 text-xs bg-gray-100 dark:bg-gray-800 px-2 py-0.5 rounded-full">
            {counts.refunded}
          </span>
        </TabsTrigger>
      </TabsList>
    </Tabs>
  );
}