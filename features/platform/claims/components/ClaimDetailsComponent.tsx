'use client';

import { useState } from 'react';
import { ClaimOrder } from '../lib/types';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { EditItemDrawer } from '@/features/dashboard/components/EditItemDrawer';
import { 
  FileQuestion, Package, DollarSign, Calendar, 
  MapPin, AlertCircle, Image, Tag, RefreshCw,
  Truck, FileText
} from 'lucide-react';
import { format } from 'date-fns';

interface ClaimDetailsComponentProps {
  claim: ClaimOrder;
}

export function ClaimDetailsComponent({ claim }: ClaimDetailsComponentProps) {
  const [isEditOpen, setIsEditOpen] = useState(false);

  const formatDate = (date: string) => {
    return format(new Date(date), 'MMM d, yyyy');
  };

  const formatCurrency = (amount: number) => {
    return `$${(amount / 100).toFixed(2)}`;
  };

  const getTypeBadge = () => {
    switch (claim.type) {
      case 'refund':
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">Refund</Badge>;
      case 'replace':
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Replace</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const getPaymentStatusBadge = () => {
    switch (claim.paymentStatus) {
      case 'refunded':
        return <Badge variant="default" className="bg-green-600">Refunded</Badge>;
      case 'not_refunded':
        return <Badge variant="secondary">Not Refunded</Badge>;
      case 'na':
        return <Badge variant="outline">N/A</Badge>;
      default:
        return null;
    }
  };

  const getFulfillmentStatusBadge = () => {
    switch (claim.fulfillmentStatus) {
      case 'fulfilled':
      case 'shipped':
        return <Badge variant="default">Fulfilled</Badge>;
      case 'partially_fulfilled':
      case 'partially_shipped':
        return <Badge variant="outline">Partial</Badge>;
      case 'canceled':
        return <Badge variant="destructive">Canceled</Badge>;
      case 'requires_action':
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">Action Required</Badge>;
      default:
        return <Badge variant="secondary">Pending</Badge>;
    }
  };

  const getTotalItems = () => {
    if (!claim.claimItems) return 0;
    return claim.claimItems.reduce((sum, item) => sum + (item.quantity || 0), 0);
  };

  const getClaimReasonIcon = (reason?: string | null) => {
    switch (reason) {
      case 'missing_item':
        return <Package className="w-4 h-4" />;
      case 'wrong_item':
        return <AlertCircle className="w-4 h-4" />;
      default:
        return <FileQuestion className="w-4 h-4" />;
    }
  };

  return (
    <>
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm hover:shadow-md transition-shadow">
        <Accordion type="single" collapsible className="w-full">
          <AccordionItem value={claim.id} className="border-none">
            <AccordionTrigger className="hover:no-underline px-6 py-4">
              <div className="flex items-center justify-between w-full pr-4">
                <div className="flex items-center gap-4">
                  <div className="w-10 h-10 bg-gradient-to-br from-rose-500 to-rose-600 rounded-full flex items-center justify-center text-white">
                    <FileQuestion className="w-5 h-5" />
                  </div>
                  <div className="text-left">
                    <h3 className="font-semibold text-gray-900 dark:text-gray-100">
                      Claim for Order #{claim.order?.orderNumber || 'N/A'}
                    </h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {getTotalItems()} items • {claim.refundAmount ? formatCurrency(claim.refundAmount) : 'No refund amount'}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {getTypeBadge()}
                  {getPaymentStatusBadge()}
                  {getFulfillmentStatusBadge()}
                </div>
              </div>
            </AccordionTrigger>
            <AccordionContent className="px-6 pb-4">
              <div className="space-y-4">
                <div className="flex flex-wrap gap-4 text-sm text-gray-600 dark:text-gray-400">
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4" />
                    Created: {formatDate(claim.createdAt)}
                  </div>
                  {claim.canceledAt && (
                    <div className="flex items-center gap-2">
                      <Calendar className="w-4 h-4" />
                      Canceled: {formatDate(claim.canceledAt)}
                    </div>
                  )}
                  {claim.return && (
                    <div className="flex items-center gap-2">
                      <RefreshCw className="w-4 h-4" />
                      Has return ({claim.return.status})
                    </div>
                  )}
                </div>

                {claim.order && (
                  <div className="border-t pt-4">
                    <h4 className="font-medium text-sm mb-2 flex items-center gap-2">
                      <FileText className="w-4 h-4" />
                      Order Details
                    </h4>
                    <div className="space-y-1 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Order Number</span>
                        <span>#{claim.order.orderNumber}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Order Total</span>
                        <span>{formatCurrency(claim.order.total)}</span>
                      </div>
                      {claim.refundAmount && (
                        <div className="flex justify-between font-medium">
                          <span>Claim Amount</span>
                          <span className="text-green-600">{formatCurrency(claim.refundAmount)}</span>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {claim.claimItems && claim.claimItems.length > 0 && (
                  <div className="border-t pt-4">
                    <h4 className="font-medium text-sm mb-2 flex items-center gap-2">
                      <Package className="w-4 h-4" />
                      Claim Items ({claim.claimItems.length})
                    </h4>
                    <div className="space-y-3">
                      {claim.claimItems.map((item) => (
                        <div key={item.id} className="bg-gray-50 dark:bg-gray-900 p-3 rounded text-sm">
                          <div className="flex justify-between mb-1">
                            <span className="font-medium flex items-center gap-2">
                              {getClaimReasonIcon(item.reason)}
                              {item.productVariant ? 
                                `${item.productVariant.product?.title} - ${item.productVariant.title}` : 
                                item.lineItem?.title || 'Unknown Item'
                              }
                            </span>
                            <span>Qty: {item.quantity}</span>
                          </div>
                          {item.reason && (
                            <p className="text-gray-600 dark:text-gray-400">
                              Reason: {item.reason.replace('_', ' ')}
                            </p>
                          )}
                          {item.note && (
                            <p className="text-gray-600 dark:text-gray-400 italic">
                              Note: {item.note}
                            </p>
                          )}
                          <div className="flex flex-wrap gap-2 mt-2">
                            {item.claimImages && item.claimImages.length > 0 && (
                              <Badge variant="outline" className="text-xs">
                                <Image className="w-3 h-3 mr-1" />
                                {item.claimImages.length} images
                              </Badge>
                            )}
                            {item.claimTags && item.claimTags.map((tag) => (
                              <Badge key={tag.id} variant="secondary" className="text-xs">
                                <Tag className="w-3 h-3 mr-1" />
                                {tag.value}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {claim.address && (
                  <div className="border-t pt-4">
                    <h4 className="font-medium text-sm mb-2 flex items-center gap-2">
                      <MapPin className="w-4 h-4" />
                      Shipping Address
                    </h4>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      <p>{claim.address.line1}</p>
                      {claim.address.line2 && <p>{claim.address.line2}</p>}
                      <p>
                        {claim.address.city}, {claim.address.provinceCode} {claim.address.postalCode}
                      </p>
                      <p>{claim.address.countryCode}</p>
                    </div>
                  </div>
                )}

                {claim.shippingMethods && claim.shippingMethods.length > 0 && (
                  <div className="border-t pt-4">
                    <h4 className="font-medium text-sm mb-2 flex items-center gap-2">
                      <Truck className="w-4 h-4" />
                      Shipping Methods
                    </h4>
                    <div className="space-y-1">
                      {claim.shippingMethods.map((method) => (
                        <p key={method.id} className="text-sm text-gray-600 dark:text-gray-400">
                          {method.shippingOption?.name || 'Standard Shipping'}
                        </p>
                      ))}
                    </div>
                  </div>
                )}

                <div className="flex justify-end pt-2">
                  <Button
                    onClick={() => setIsEditOpen(true)}
                    variant="outline"
                    size="sm"
                  >
                    Manage Claim
                  </Button>
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </div>

      <EditItemDrawer
        isOpen={isEditOpen}
        onClose={() => setIsEditOpen(false)}
        itemId={claim.id}
        listKey="ClaimOrder"
      />
    </>
  );
}