export interface ClaimOrder {
  id: string;
  paymentStatus?: 'na' | 'not_refunded' | 'refunded' | null;
  fulfillmentStatus?: 'not_fulfilled' | 'partially_fulfilled' | 'fulfilled' | 'partially_shipped' | 'shipped' | 'partially_returned' | 'returned' | 'canceled' | 'requires_action' | null;
  type?: 'refund' | 'replace' | null;
  refundAmount?: number | null;
  canceledAt?: string | null;
  metadata?: any;
  idempotencyKey?: string | null;
  noNotification?: boolean | null;
  address?: {
    id: string;
    line1: string;
    line2?: string | null;
    city: string;
    provinceCode?: string | null;
    postalCode: string;
    countryCode: string;
  } | null;
  order?: {
    id: string;
    orderNumber: string;
    total: number;
  } | null;
  claimItems?: Array<{
    id: string;
    reason?: string | null;
    note?: string | null;
    quantity: number;
    metadata?: any;
    productVariant?: {
      id: string;
      title: string;
      product?: {
        id: string;
        title: string;
      } | null;
    } | null;
    lineItem?: {
      id: string;
      title: string;
    } | null;
    claimImages?: Array<{
      id: string;
      url: string;
    }>;
    claimTags?: Array<{
      id: string;
      value: string;
    }>;
  }>;
  fulfillments?: Array<{
    id: string;
    status: string;
  }>;
  lineItems?: Array<{
    id: string;
    title: string;
    quantity: number;
  }>;
  return?: {
    id: string;
    status?: string | null;
  } | null;
  shippingMethods?: Array<{
    id: string;
    shippingOption?: {
      id: string;
      name: string;
    } | null;
  }>;
  createdAt: string;
  updatedAt: string;
}

export interface ClaimFilters {
  search?: string;
  type?: string;
  paymentStatus?: string;
  fulfillmentStatus?: string;
}

export interface ClaimStatusCounts {
  all: number;
  refund: number;
  replace: number;
  notRefunded: number;
  refunded: number;
}