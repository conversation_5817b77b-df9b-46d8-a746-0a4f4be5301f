'use server';

import { keystoneClient } from '@/features/dashboard/lib/keystoneClient';
import { ClaimFilters, ClaimStatusCounts } from '../lib/types';

export async function getClaims({
  page = 1,
  perPage = 20,
  search = '',
  sortBy = 'createdAt_DESC',
  filters = {}
}: {
  page?: number;
  perPage?: number;
  search?: string;
  sortBy?: string;
  filters?: ClaimFilters;
}) {
  const where: any = {};
  
  if (search) {
    where.OR = [
      { order: { orderNumber: { contains: search, mode: 'insensitive' } } },
      { idempotencyKey: { contains: search, mode: 'insensitive' } }
    ];
  }
  
  if (filters.type) {
    where.type = { equals: filters.type };
  }
  
  if (filters.paymentStatus) {
    where.paymentStatus = { equals: filters.paymentStatus };
  }
  
  if (filters.fulfillmentStatus) {
    where.fulfillmentStatus = { equals: filters.fulfillmentStatus };
  }
  
  const query = `
    query ClaimsQuery($where: ClaimOrderWhereInput, $orderBy: [ClaimOrderOrderByInput!], $take: Int, $skip: Int) {
      claimOrders(where: $where, orderBy: $orderBy, take: $take, skip: $skip) {
        id
        paymentStatus
        fulfillmentStatus
        type
        refundAmount
        canceledAt
        metadata
        idempotencyKey
        noNotification
        address {
          id
          line1
          line2
          city
          provinceCode
          postalCode
          countryCode
        }
        order {
          id
          orderNumber
          total
        }
        claimItems {
          id
          reason
          note
          quantity
          metadata
          productVariant {
            id
            title
            product {
              id
              title
            }
          }
          lineItem {
            id
            title
          }
          claimImages {
            id
            url
          }
          claimTags {
            id
            value
          }
        }
        fulfillments {
          id
          status
        }
        lineItems {
          id
          title
          quantity
        }
        return {
          id
          status
        }
        shippingMethods {
          id
          shippingOption {
            id
            name
          }
        }
        createdAt
        updatedAt
      }
      claimOrdersCount(where: $where)
    }
  `;

  const response = await keystoneClient(query, {
    where,
    orderBy: [sortBy],
    take: perPage,
    skip: (page - 1) * perPage
  });
  
  if (!response.success) {
    throw new Error(response.error);
  }
  
  return {
    claims: response.data.claimOrders || [],
    totalCount: response.data.claimOrdersCount || 0
  };
}

export async function getClaimStatusCounts() {
  const query = `
    query ClaimStatusCounts {
      all: claimOrdersCount
      refund: claimOrdersCount(where: { type: { equals: refund } })
      replace: claimOrdersCount(where: { type: { equals: replace } })
      notRefunded: claimOrdersCount(where: { paymentStatus: { equals: not_refunded } })
      refunded: claimOrdersCount(where: { paymentStatus: { equals: refunded } })
    }
  `;
  
  const response = await keystoneClient(query);
  
  if (!response.success) {
    throw new Error(response.error);
  }
  
  return {
    all: response.data.all || 0,
    refund: response.data.refund || 0,
    replace: response.data.replace || 0,
    notRefunded: response.data.notRefunded || 0,
    refunded: response.data.refunded || 0
  };
}