import { Suspense } from 'react';
import { PageBreadcrumbs } from '@/features/dashboard/components/PageBreadcrumbs';
import { PlatformFilterBar } from '@/features/dashboard/components/PlatformFilterBar';
import { PaginationWrapper } from '@/features/dashboard/components/PaginationWrapper';
import { ClaimDetailsComponent } from '../components/ClaimDetailsComponent';
import { StatusTabs } from '../components/StatusTabs';
import { getClaims, getClaimStatusCounts } from '../actions';
import { Triangle, Square, Circle } from 'lucide-react';

interface ClaimsListPageProps {
  searchParams: {
    page?: string;
    search?: string;
    sortBy?: string;
    status?: string;
  };
}

async function ClaimsContent({ searchParams }: ClaimsListPageProps) {
  const page = parseInt(searchParams.page || '1');
  const search = searchParams.search || '';
  const sortBy = searchParams.sortBy || 'createdAt_DESC';
  const status = searchParams.status || 'all';

  const filters: any = {};
  if (status === 'refund') {
    filters.type = 'refund';
  } else if (status === 'replace') {
    filters.type = 'replace';
  } else if (status === 'notRefunded') {
    filters.paymentStatus = 'not_refunded';
  } else if (status === 'refunded') {
    filters.paymentStatus = 'refunded';
  }

  const [{ claims, totalCount }, statusCounts] = await Promise.all([
    getClaims({ page, search, sortBy, filters }),
    getClaimStatusCounts()
  ]);

  const totalPages = Math.ceil(totalCount / 20);

  return (
    <>
      <div className="mb-6">
        <StatusTabs
          counts={statusCounts}
          currentStatus={status}
          onStatusChange={() => {}}
        />
      </div>

      {claims.length === 0 ? (
        <div className="text-center py-12">
          <div className="flex justify-center items-center mb-4 space-x-2">
            <Triangle className="w-8 h-8 text-gray-300 animate-pulse" />
            <Square className="w-8 h-8 text-gray-300 animate-pulse delay-75" />
            <Circle className="w-8 h-8 text-gray-300 animate-pulse delay-150" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
            No claims found
          </h3>
          <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
            {search ? 'Try adjusting your search terms' : 'Customer claims will appear here'}
          </p>
        </div>
      ) : (
        <>
          <div className="grid gap-4">
            {claims.map((claim) => (
              <ClaimDetailsComponent key={claim.id} claim={claim} />
            ))}
          </div>

          <div className="mt-8">
            <PaginationWrapper
              currentPage={page}
              totalPages={totalPages}
              basePath="/platform/claims"
            />
          </div>
        </>
      )}
    </>
  );
}

export default async function ClaimsListPage({ searchParams }: ClaimsListPageProps) {
  return (
    <div className="container mx-auto py-8">
      <PageBreadcrumbs
        items={[
          { label: 'Dashboard', href: '/dashboard', type: 'link' },
          { label: 'Platform', href: '/dashboard/platform', type: 'link' },
          { label: 'Claims', type: 'page' }
        ]}
      />

      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
          Claims
        </h1>
        <p className="mt-2 text-gray-600 dark:text-gray-400">
          Handle customer claims, disputes, and resolution processes.
        </p>
      </div>

      <div className="mb-6">
        <PlatformFilterBar
          searchPlaceholder="Search claims by order number..."
          sortOptions={[
            { label: 'Newest First', value: 'createdAt_DESC' },
            { label: 'Oldest First', value: 'createdAt_ASC' },
            { label: 'Refund Amount (High to Low)', value: 'refundAmount_DESC' },
            { label: 'Refund Amount (Low to High)', value: 'refundAmount_ASC' }
          ]}
        />
      </div>

      <Suspense fallback={<div>Loading claims...</div>}>
        <ClaimsContent searchParams={searchParams} />
      </Suspense>
    </div>
  );
}