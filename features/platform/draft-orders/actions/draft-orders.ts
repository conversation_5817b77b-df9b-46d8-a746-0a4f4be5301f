'use server';

import { keystoneClient } from '@/features/dashboard/lib/keystoneClient';
import { DraftOrderFilters, DraftOrderStatusCounts } from '../lib/types';

export async function getDraftOrders({
  page = 1,
  perPage = 20,
  search = '',
  sortBy = 'createdAt_DESC',
  filters = {}
}: {
  page?: number;
  perPage?: number;
  search?: string;
  sortBy?: string;
  filters?: DraftOrderFilters;
}) {
  const where: any = {};
  
  if (search) {
    where.OR = [
      { displayId: { equals: parseInt(search) || 0 } },
      { cart: { email: { contains: search, mode: 'insensitive' } } }
    ];
  }
  
  if (filters.status === 'open') {
    where.status = { equals: 'open' };
  } else if (filters.status === 'completed') {
    where.status = { equals: 'completed' };
  } else if (filters.status === 'canceled') {
    where.canceledAt = { not: null };
  }
  
  const query = `
    query DraftOrdersQuery($where: DraftOrderWhereInput, $orderBy: [DraftOrderOrderByInput!], $take: Int, $skip: Int) {
      draftOrders(where: $where, orderBy: $orderBy, take: $take, skip: $skip) {
        id
        status
        displayId
        canceledAt
        completedAt
        metadata
        idempotencyKey
        noNotificationOrder
        cart {
          id
          email
          total
          subtotal
          shippingTotal
          taxTotal
          discountTotal
          currencyCode
          lineItems {
            id
            title
            quantity
            unitPrice
            variant {
              id
              title
            }
          }
        }
        order {
          id
          orderNumber
        }
        createdAt
        updatedAt
      }
      draftOrdersCount(where: $where)
    }
  `;

  const response = await keystoneClient(query, {
    where,
    orderBy: [sortBy],
    take: perPage,
    skip: (page - 1) * perPage
  });
  
  if (!response.success) {
    throw new Error(response.error);
  }
  
  return {
    draftOrders: response.data.draftOrders || [],
    totalCount: response.data.draftOrdersCount || 0
  };
}

export async function getDraftOrderStatusCounts() {
  const query = `
    query DraftOrderStatusCounts {
      all: draftOrdersCount
      open: draftOrdersCount(where: { status: { equals: open } })
      completed: draftOrdersCount(where: { status: { equals: completed } })
      canceled: draftOrdersCount(where: { canceledAt: { not: null } })
    }
  `;
  
  const response = await keystoneClient(query);
  
  if (!response.success) {
    throw new Error(response.error);
  }
  
  return {
    all: response.data.all || 0,
    open: response.data.open || 0,
    completed: response.data.completed || 0,
    canceled: response.data.canceled || 0
  };
}