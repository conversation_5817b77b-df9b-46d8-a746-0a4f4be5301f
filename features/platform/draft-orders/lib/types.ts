export interface DraftOrder {
  id: string;
  status: 'open' | 'completed';
  displayId: number;
  canceledAt?: string | null;
  completedAt?: string | null;
  metadata?: any;
  idempotencyKey?: string | null;
  noNotificationOrder: boolean;
  cart?: {
    id: string;
    email?: string | null;
    total: number;
    subtotal: number;
    shippingTotal: number;
    taxTotal: number;
    discountTotal: number;
    currencyCode: string;
    lineItems?: Array<{
      id: string;
      title: string;
      quantity: number;
      unitPrice: number;
      variant?: {
        id: string;
        title: string;
      } | null;
    }>;
  } | null;
  order?: {
    id: string;
    orderNumber: string;
  } | null;
  createdAt: string;
  updatedAt: string;
}

export interface DraftOrderFilters {
  search?: string;
  status?: string;
}

export interface DraftOrderStatusCounts {
  all: number;
  open: number;
  completed: number;
  canceled: number;
}