import { Suspense } from 'react';
import { PageBreadcrumbs } from '@/features/dashboard/components/PageBreadcrumbs';
import { PlatformFilterBar } from '@/features/dashboard/components/PlatformFilterBar';
import { PaginationWrapper } from '@/features/dashboard/components/PaginationWrapper';
import { DraftOrderDetailsComponent } from '../components/DraftOrderDetailsComponent';
import { StatusTabs } from '../components/StatusTabs';
import { getDraftOrders, getDraftOrderStatusCounts } from '../actions';
import { Triangle, Square, Circle } from 'lucide-react';

interface DraftOrdersListPageProps {
  searchParams: {
    page?: string;
    search?: string;
    sortBy?: string;
    status?: string;
  };
}

async function DraftOrdersContent({ searchParams }: DraftOrdersListPageProps) {
  const page = parseInt(searchParams.page || '1');
  const search = searchParams.search || '';
  const sortBy = searchParams.sortBy || 'createdAt_DESC';
  const status = searchParams.status || 'all';

  const filters: any = {};
  if (status !== 'all') {
    filters.status = status;
  }

  const [{ draftOrders, totalCount }, statusCounts] = await Promise.all([
    getDraftOrders({ page, search, sortBy, filters }),
    getDraftOrderStatusCounts()
  ]);

  const totalPages = Math.ceil(totalCount / 20);

  return (
    <>
      <div className="mb-6">
        <StatusTabs
          counts={statusCounts}
          currentStatus={status}
          onStatusChange={() => {}}
        />
      </div>

      {draftOrders.length === 0 ? (
        <div className="text-center py-12">
          <div className="flex justify-center items-center mb-4 space-x-2">
            <Triangle className="w-8 h-8 text-gray-300 animate-pulse" />
            <Square className="w-8 h-8 text-gray-300 animate-pulse delay-75" />
            <Circle className="w-8 h-8 text-gray-300 animate-pulse delay-150" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
            No draft orders found
          </h3>
          <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
            {search ? 'Try adjusting your search terms' : 'Create your first draft order to get started'}
          </p>
        </div>
      ) : (
        <>
          <div className="grid gap-4">
            {draftOrders.map((draftOrder) => (
              <DraftOrderDetailsComponent key={draftOrder.id} draftOrder={draftOrder} />
            ))}
          </div>

          <div className="mt-8">
            <PaginationWrapper
              currentPage={page}
              totalPages={totalPages}
              basePath="/platform/draft-orders"
            />
          </div>
        </>
      )}
    </>
  );
}

export default async function DraftOrdersListPage({ searchParams }: DraftOrdersListPageProps) {
  return (
    <div className="container mx-auto py-8">
      <PageBreadcrumbs
        items={[
          { label: 'Dashboard', href: '/dashboard' },
          { label: 'Platform', href: '/dashboard/platform' },
          { label: 'Draft Orders' }
        ]}
      />

      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
          Draft Orders
        </h1>
        <p className="mt-2 text-gray-600 dark:text-gray-400">
          Create and manage draft orders before finalizing customer purchases.
        </p>
      </div>

      <div className="mb-6">
        <PlatformFilterBar
          searchPlaceholder="Search draft orders by ID or email..."
          sortOptions={[
            { label: 'Newest First', value: 'createdAt_DESC' },
            { label: 'Oldest First', value: 'createdAt_ASC' },
            { label: 'ID (High to Low)', value: 'displayId_DESC' },
            { label: 'ID (Low to High)', value: 'displayId_ASC' }
          ]}
        />
      </div>

      <Suspense fallback={<div>Loading draft orders...</div>}>
        <DraftOrdersContent searchParams={searchParams} />
      </Suspense>
    </div>
  );
}