'use client';

import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { DraftOrderStatusCounts } from '../lib/types';

interface StatusTabsProps {
  counts: DraftOrderStatusCounts;
  currentStatus: string;
  onStatusChange: (status: string) => void;
}

export function StatusTabs({ counts, currentStatus, onStatusChange }: StatusTabsProps) {
  return (
    <Tabs value={currentStatus} onValueChange={onStatusChange}>
      <TabsList>
        <TabsTrigger value="all">
          All Draft Orders
          <span className="ml-2 text-xs bg-gray-100 dark:bg-gray-800 px-2 py-0.5 rounded-full">
            {counts.all}
          </span>
        </TabsTrigger>
        <TabsTrigger value="open">
          Open
          <span className="ml-2 text-xs bg-gray-100 dark:bg-gray-800 px-2 py-0.5 rounded-full">
            {counts.open}
          </span>
        </TabsTrigger>
        <TabsTrigger value="completed">
          Completed
          <span className="ml-2 text-xs bg-gray-100 dark:bg-gray-800 px-2 py-0.5 rounded-full">
            {counts.completed}
          </span>
        </TabsTrigger>
        <TabsTrigger value="canceled">
          Canceled
          <span className="ml-2 text-xs bg-gray-100 dark:bg-gray-800 px-2 py-0.5 rounded-full">
            {counts.canceled}
          </span>
        </TabsTrigger>
      </TabsList>
    </Tabs>
  );
}