'use client';

import { useState } from 'react';
import { DraftOrder } from '../lib/types';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { EditItemDrawer } from '@/features/dashboard/components/EditItemDrawer';
import { FileText, Mail, ShoppingCart, Package, DollarSign, Calendar } from 'lucide-react';
import { format } from 'date-fns';

interface DraftOrderDetailsComponentProps {
  draftOrder: DraftOrder;
}

export function DraftOrderDetailsComponent({ draftOrder }: DraftOrderDetailsComponentProps) {
  const [isEditOpen, setIsEditOpen] = useState(false);

  const formatDate = (date: string) => {
    return format(new Date(date), 'MMM d, yyyy h:mm a');
  };

  const formatCurrency = (amount: number, currencyCode: string = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currencyCode,
    }).format(amount / 100);
  };

  const getStatusBadge = () => {
    if (draftOrder.canceledAt) {
      return <Badge variant="secondary">Canceled</Badge>;
    }
    if (draftOrder.status === 'completed') {
      return <Badge variant="default">Completed</Badge>;
    }
    return <Badge variant="outline">Open</Badge>;
  };

  return (
    <>
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm hover:shadow-md transition-shadow">
        <Accordion type="single" collapsible className="w-full">
          <AccordionItem value={draftOrder.id} className="border-none">
            <AccordionTrigger className="hover:no-underline px-6 py-4">
              <div className="flex items-center justify-between w-full pr-4">
                <div className="flex items-center gap-4">
                  <div className="w-10 h-10 bg-gradient-to-br from-stone-500 to-stone-600 rounded-full flex items-center justify-center text-white">
                    <FileText className="w-5 h-5" />
                  </div>
                  <div className="text-left">
                    <h3 className="font-semibold text-gray-900 dark:text-gray-100">
                      Draft #{draftOrder.displayId}
                    </h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {draftOrder.cart?.email || 'No email provided'}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-4">
                  {getStatusBadge()}
                  {draftOrder.cart && (
                    <span className="hidden sm:inline-flex font-medium">
                      {formatCurrency(draftOrder.cart.total, draftOrder.cart.currencyCode)}
                    </span>
                  )}
                </div>
              </div>
            </AccordionTrigger>
            <AccordionContent className="px-6 pb-4">
              <div className="space-y-4">
                <div className="flex flex-wrap gap-4 text-sm text-gray-600 dark:text-gray-400">
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4" />
                    Created: {formatDate(draftOrder.createdAt)}
                  </div>
                  {draftOrder.completedAt && (
                    <div className="flex items-center gap-2">
                      <Calendar className="w-4 h-4" />
                      Completed: {formatDate(draftOrder.completedAt)}
                    </div>
                  )}
                  {draftOrder.canceledAt && (
                    <div className="flex items-center gap-2">
                      <Calendar className="w-4 h-4" />
                      Canceled: {formatDate(draftOrder.canceledAt)}
                    </div>
                  )}
                </div>

                {draftOrder.cart && (
                  <>
                    <div className="border-t pt-4">
                      <h4 className="font-medium text-sm mb-2 flex items-center gap-2">
                        <DollarSign className="w-4 h-4" />
                        Order Summary
                      </h4>
                      <div className="space-y-1 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600 dark:text-gray-400">Subtotal</span>
                          <span>{formatCurrency(draftOrder.cart.subtotal, draftOrder.cart.currencyCode)}</span>
                        </div>
                        {draftOrder.cart.discountTotal > 0 && (
                          <div className="flex justify-between">
                            <span className="text-gray-600 dark:text-gray-400">Discount</span>
                            <span className="text-green-600">-{formatCurrency(draftOrder.cart.discountTotal, draftOrder.cart.currencyCode)}</span>
                          </div>
                        )}
                        <div className="flex justify-between">
                          <span className="text-gray-600 dark:text-gray-400">Shipping</span>
                          <span>{formatCurrency(draftOrder.cart.shippingTotal, draftOrder.cart.currencyCode)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600 dark:text-gray-400">Tax</span>
                          <span>{formatCurrency(draftOrder.cart.taxTotal, draftOrder.cart.currencyCode)}</span>
                        </div>
                        <div className="flex justify-between font-medium pt-2 border-t">
                          <span>Total</span>
                          <span>{formatCurrency(draftOrder.cart.total, draftOrder.cart.currencyCode)}</span>
                        </div>
                      </div>
                    </div>

                    {draftOrder.cart.lineItems && draftOrder.cart.lineItems.length > 0 && (
                      <div className="border-t pt-4">
                        <h4 className="font-medium text-sm mb-2 flex items-center gap-2">
                          <Package className="w-4 h-4" />
                          Items ({draftOrder.cart.lineItems.length})
                        </h4>
                        <div className="space-y-2">
                          {draftOrder.cart.lineItems.map((item) => (
                            <div key={item.id} className="flex justify-between text-sm">
                              <div className="text-gray-600 dark:text-gray-400">
                                <span>{item.title}</span>
                                {item.variant && (
                                  <span className="text-xs ml-2">({item.variant.title})</span>
                                )}
                                <span className="text-xs ml-2">x{item.quantity}</span>
                              </div>
                              <span className="font-medium">
                                {formatCurrency(item.unitPrice * item.quantity, draftOrder.cart!.currencyCode)}
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </>
                )}

                {draftOrder.order && (
                  <div className="border-t pt-4">
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Converted to order #{draftOrder.order.orderNumber}
                    </p>
                  </div>
                )}

                <div className="flex justify-end pt-2">
                  <Button
                    onClick={() => setIsEditOpen(true)}
                    variant="outline"
                    size="sm"
                  >
                    Edit Draft Order
                  </Button>
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </div>

      <EditItemDrawer
        isOpen={isEditOpen}
        onClose={() => setIsEditOpen(false)}
        itemId={draftOrder.id}
        listKey="DraftOrder"
      />
    </>
  );
}