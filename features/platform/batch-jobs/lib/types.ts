export interface BatchJob {
  id: string;
  type?: 'PRODUCT_IMPORT' | 'ORDER_EXPORT' | 'INVENTORY_UPDATE' | 'PRICE_UPDATE' | null;
  status?: 'CREATED' | 'PROCESSING' | 'COMPLETED' | 'FAILED' | 'CANCELED' | null;
  context?: any;
  result?: any;
  error?: string | null;
  progress?: number | null;
  createdBy?: {
    id: string;
    name: string;
    email: string;
  } | null;
  completedAt?: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface BatchJobFilters {
  search?: string;
  type?: string;
  status?: string;
}

export interface BatchJobStatusCounts {
  all: number;
  created: number;
  processing: number;
  completed: number;
  failed: number;
  canceled: number;
}