import { Suspense } from 'react';
import { PageBreadcrumbs } from '@/features/dashboard/components/PageBreadcrumbs';
import { PlatformFilterBar } from '@/features/dashboard/components/PlatformFilterBar';
import { PaginationWrapper } from '@/features/dashboard/components/PaginationWrapper';
import { BatchJobDetailsComponent } from '../components/BatchJobDetailsComponent';
import { StatusTabs } from '../components/StatusTabs';
import { getBatchJobs, getBatchJobStatusCounts } from '../actions';
import { Triangle, Square, Circle } from 'lucide-react';

interface BatchJobsListPageProps {
  searchParams: {
    page?: string;
    search?: string;
    sortBy?: string;
    status?: string;
    type?: string;
  };
}

async function BatchJobsContent({ searchParams }: BatchJobsListPageProps) {
  const page = parseInt(searchParams.page || '1');
  const search = searchParams.search || '';
  const sortBy = searchParams.sortBy || 'createdAt_DESC';
  const status = searchParams.status || 'all';

  const filters: any = {};
  if (status !== 'all') {
    filters.status = status.toUpperCase();
  }
  if (searchParams.type) {
    filters.type = searchParams.type;
  }

  const [{ batchJobs, totalCount }, statusCounts] = await Promise.all([
    getBatchJobs({ page, search, sortBy, filters }),
    getBatchJobStatusCounts()
  ]);

  const totalPages = Math.ceil(totalCount / 20);

  return (
    <>
      <div className="mb-6">
        <StatusTabs
          counts={statusCounts}
          currentStatus={status}
          onStatusChange={() => {}}
        />
      </div>

      {batchJobs.length === 0 ? (
        <div className="text-center py-12">
          <div className="flex justify-center items-center mb-4 space-x-2">
            <Triangle className="w-8 h-8 text-gray-300 animate-pulse" />
            <Square className="w-8 h-8 text-gray-300 animate-pulse delay-75" />
            <Circle className="w-8 h-8 text-gray-300 animate-pulse delay-150" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
            No batch jobs found
          </h3>
          <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
            {search ? 'Try adjusting your search terms' : 'Batch jobs will appear here when they are created'}
          </p>
        </div>
      ) : (
        <>
          <div className="grid gap-4">
            {batchJobs.map((batchJob) => (
              <BatchJobDetailsComponent key={batchJob.id} batchJob={batchJob} />
            ))}
          </div>

          <div className="mt-8">
            <PaginationWrapper
              currentPage={page}
              totalPages={totalPages}
              basePath="/platform/batch-jobs"
            />
          </div>
        </>
      )}
    </>
  );
}

export default async function BatchJobsListPage({ searchParams }: BatchJobsListPageProps) {
  return (
    <div className="container mx-auto py-8">
      <PageBreadcrumbs
        items={[
          { label: 'Dashboard', href: '/dashboard', type: 'link' },
          { label: 'Platform', href: '/dashboard/platform', type: 'link' },
          { label: 'Batch Jobs', type: 'page' }
        ]}
      />

      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
          Batch Jobs
        </h1>
        <p className="mt-2 text-gray-600 dark:text-gray-400">
          Monitor and manage background tasks and bulk operations.
        </p>
      </div>

      <div className="mb-6">
        <PlatformFilterBar
          searchPlaceholder="Search batch jobs..."
          sortOptions={[
            { label: 'Newest First', value: 'createdAt_DESC' },
            { label: 'Oldest First', value: 'createdAt_ASC' },
            { label: 'Progress (High to Low)', value: 'progress_DESC' },
            { label: 'Progress (Low to High)', value: 'progress_ASC' }
          ]}
        />
      </div>

      <Suspense fallback={<div>Loading batch jobs...</div>}>
        <BatchJobsContent searchParams={searchParams} />
      </Suspense>
    </div>
  );
}