'use server';

import { keystoneClient } from '@/features/dashboard/lib/keystoneClient';
import { BatchJobFilters, BatchJobStatusCounts } from '../lib/types';

export async function getBatchJobs({
  page = 1,
  perPage = 20,
  search = '',
  sortBy = 'createdAt_DESC',
  filters = {}
}: {
  page?: number;
  perPage?: number;
  search?: string;
  sortBy?: string;
  filters?: BatchJobFilters;
}) {
  const where: any = {};
  
  if (search) {
    where.OR = [
      { error: { contains: search, mode: 'insensitive' } },
      { createdBy: { name: { contains: search, mode: 'insensitive' } } }
    ];
  }
  
  if (filters.type) {
    where.type = { equals: filters.type };
  }
  
  if (filters.status) {
    where.status = { equals: filters.status };
  }
  
  const query = `
    query BatchJobsQuery($where: BatchJobWhereInput, $orderBy: [BatchJobOrderByInput!], $take: Int, $skip: Int) {
      batchJobs(where: $where, orderBy: $orderBy, take: $take, skip: $skip) {
        id
        type
        status
        context
        result
        error
        progress
        createdBy {
          id
          name
          email
        }
        completedAt
        createdAt
        updatedAt
      }
      batchJobsCount(where: $where)
    }
  `;

  const response = await keystoneClient(query, {
    where,
    orderBy: [sortBy],
    take: perPage,
    skip: (page - 1) * perPage
  });
  
  if (!response.success) {
    throw new Error(response.error);
  }
  
  return {
    batchJobs: response.data.batchJobs || [],
    totalCount: response.data.batchJobsCount || 0
  };
}

export async function getBatchJobStatusCounts() {
  const query = `
    query BatchJobStatusCounts {
      all: batchJobsCount
      created: batchJobsCount(where: { status: { equals: CREATED } })
      processing: batchJobsCount(where: { status: { equals: PROCESSING } })
      completed: batchJobsCount(where: { status: { equals: COMPLETED } })
      failed: batchJobsCount(where: { status: { equals: FAILED } })
      canceled: batchJobsCount(where: { status: { equals: CANCELED } })
    }
  `;
  
  const response = await keystoneClient(query);
  
  if (!response.success) {
    throw new Error(response.error);
  }
  
  return {
    all: response.data.all || 0,
    created: response.data.created || 0,
    processing: response.data.processing || 0,
    completed: response.data.completed || 0,
    failed: response.data.failed || 0,
    canceled: response.data.canceled || 0
  };
}