'use client';

import { useState } from 'react';
import { BatchJob } from '../lib/types';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { EditItemDrawer } from '@/features/dashboard/components/EditItemDrawer';
import { Progress } from '@/components/ui/progress';
import { 
  FileUp, FileDown, Package, DollarSign, 
  User, Calendar, AlertCircle, CheckCircle,
  XCircle, Loader2, PlayCircle
} from 'lucide-react';
import { format } from 'date-fns';

interface BatchJobDetailsComponentProps {
  batchJob: BatchJob;
}

export function BatchJobDetailsComponent({ batchJob }: BatchJobDetailsComponentProps) {
  const [isEditOpen, setIsEditOpen] = useState(false);

  const formatDate = (date: string) => {
    return format(new Date(date), 'MMM d, yyyy h:mm a');
  };

  const getStatusBadge = () => {
    switch (batchJob.status) {
      case 'CREATED':
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">Created</Badge>;
      case 'PROCESSING':
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">Processing</Badge>;
      case 'COMPLETED':
        return <Badge variant="default" className="bg-green-600">Completed</Badge>;
      case 'FAILED':
        return <Badge variant="destructive">Failed</Badge>;
      case 'CANCELED':
        return <Badge variant="secondary">Canceled</Badge>;
      default:
        return null;
    }
  };

  const getTypeIcon = () => {
    switch (batchJob.type) {
      case 'PRODUCT_IMPORT':
        return <FileUp className="w-5 h-5" />;
      case 'ORDER_EXPORT':
        return <FileDown className="w-5 h-5" />;
      case 'INVENTORY_UPDATE':
        return <Package className="w-5 h-5" />;
      case 'PRICE_UPDATE':
        return <DollarSign className="w-5 h-5" />;
      default:
        return <PlayCircle className="w-5 h-5" />;
    }
  };

  const getStatusIcon = () => {
    switch (batchJob.status) {
      case 'CREATED':
        return <PlayCircle className="w-4 h-4" />;
      case 'PROCESSING':
        return <Loader2 className="w-4 h-4 animate-spin" />;
      case 'COMPLETED':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'FAILED':
        return <XCircle className="w-4 h-4 text-red-600" />;
      case 'CANCELED':
        return <AlertCircle className="w-4 h-4 text-gray-600" />;
      default:
        return null;
    }
  };

  const getTypeName = () => {
    switch (batchJob.type) {
      case 'PRODUCT_IMPORT':
        return 'Product Import';
      case 'ORDER_EXPORT':
        return 'Order Export';
      case 'INVENTORY_UPDATE':
        return 'Inventory Update';
      case 'PRICE_UPDATE':
        return 'Price Update';
      default:
        return 'Unknown Job';
    }
  };

  return (
    <>
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm hover:shadow-md transition-shadow">
        <Accordion type="single" collapsible className="w-full">
          <AccordionItem value={batchJob.id} className="border-none">
            <AccordionTrigger className="hover:no-underline px-6 py-4">
              <div className="flex items-center justify-between w-full pr-4">
                <div className="flex items-center gap-4">
                  <div className="w-10 h-10 bg-gradient-to-br from-cyan-500 to-cyan-600 rounded-full flex items-center justify-center text-white">
                    {getTypeIcon()}
                  </div>
                  <div className="text-left">
                    <h3 className="font-semibold text-gray-900 dark:text-gray-100">
                      {getTypeName()}
                    </h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {batchJob.createdBy ? `Created by ${batchJob.createdBy.name}` : 'System generated'}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-4">
                  {getStatusBadge()}
                  {batchJob.progress !== null && batchJob.progress !== undefined && (
                    <div className="hidden sm:flex items-center gap-2 min-w-[120px]">
                      <Progress value={batchJob.progress} className="h-2" />
                      <span className="text-xs text-gray-500 whitespace-nowrap">
                        {batchJob.progress}%
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </AccordionTrigger>
            <AccordionContent className="px-6 pb-4">
              <div className="space-y-4">
                <div className="flex flex-wrap gap-4 text-sm text-gray-600 dark:text-gray-400">
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4" />
                    Started: {formatDate(batchJob.createdAt)}
                  </div>
                  {batchJob.completedAt && (
                    <div className="flex items-center gap-2">
                      <Calendar className="w-4 h-4" />
                      Completed: {formatDate(batchJob.completedAt)}
                    </div>
                  )}
                  <div className="flex items-center gap-2">
                    {getStatusIcon()}
                    Status: {batchJob.status?.toLowerCase() || 'unknown'}
                  </div>
                </div>

                {batchJob.error && (
                  <div className="border-t pt-4">
                    <h4 className="font-medium text-sm mb-2 flex items-center gap-2 text-red-600">
                      <AlertCircle className="w-4 h-4" />
                      Error Details
                    </h4>
                    <p className="text-sm text-red-600 bg-red-50 dark:bg-red-900/20 p-3 rounded">
                      {batchJob.error}
                    </p>
                  </div>
                )}

                {batchJob.context && Object.keys(batchJob.context).length > 0 && (
                  <div className="border-t pt-4">
                    <h4 className="font-medium text-sm mb-2">Job Context</h4>
                    <pre className="text-xs bg-gray-100 dark:bg-gray-900 p-3 rounded overflow-x-auto">
                      {JSON.stringify(batchJob.context, null, 2)}
                    </pre>
                  </div>
                )}

                {batchJob.result && Object.keys(batchJob.result).length > 0 && (
                  <div className="border-t pt-4">
                    <h4 className="font-medium text-sm mb-2">Job Result</h4>
                    <pre className="text-xs bg-gray-100 dark:bg-gray-900 p-3 rounded overflow-x-auto">
                      {JSON.stringify(batchJob.result, null, 2)}
                    </pre>
                  </div>
                )}

                <div className="flex justify-end pt-2">
                  <Button
                    onClick={() => setIsEditOpen(true)}
                    variant="outline"
                    size="sm"
                  >
                    View Details
                  </Button>
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </div>

      <EditItemDrawer
        isOpen={isEditOpen}
        onClose={() => setIsEditOpen(false)}
        itemId={batchJob.id}
        listKey="BatchJob"
      />
    </>
  );
}