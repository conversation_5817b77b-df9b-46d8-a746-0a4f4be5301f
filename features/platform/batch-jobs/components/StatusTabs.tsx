'use client';

import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { BatchJobStatusCounts } from '../lib/types';

interface StatusTabsProps {
  counts: BatchJobStatusCounts;
  currentStatus: string;
  onStatusChange: (status: string) => void;
}

export function StatusTabs({ counts, currentStatus, onStatusChange }: StatusTabsProps) {
  return (
    <Tabs value={currentStatus} onValueChange={onStatusChange}>
      <TabsList>
        <TabsTrigger value="all">
          All Jobs
          <span className="ml-2 text-xs bg-gray-100 dark:bg-gray-800 px-2 py-0.5 rounded-full">
            {counts.all}
          </span>
        </TabsTrigger>
        <TabsTrigger value="processing">
          Processing
          <span className="ml-2 text-xs bg-gray-100 dark:bg-gray-800 px-2 py-0.5 rounded-full">
            {counts.processing}
          </span>
        </TabsTrigger>
        <TabsTrigger value="completed">
          Completed
          <span className="ml-2 text-xs bg-gray-100 dark:bg-gray-800 px-2 py-0.5 rounded-full">
            {counts.completed}
          </span>
        </TabsTrigger>
        <TabsTrigger value="failed">
          Failed
          <span className="ml-2 text-xs bg-gray-100 dark:bg-gray-800 px-2 py-0.5 rounded-full">
            {counts.failed}
          </span>
        </TabsTrigger>
      </TabsList>
    </Tabs>
  );
}