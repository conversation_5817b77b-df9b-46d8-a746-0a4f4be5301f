'use client';

import { useState } from 'react';
import { GiftCard } from '../lib/types';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { EditItemDrawer } from '@/features/dashboard/components/EditItemDrawer';
import { Gift, Calendar, DollarSign, MapPin, Receipt, History } from 'lucide-react';
import { format, isPast } from 'date-fns';

interface GiftCardDetailsComponentProps {
  giftCard: GiftCard;
}

export function GiftCardDetailsComponent({ giftCard }: GiftCardDetailsComponentProps) {
  const [isEditOpen, setIsEditOpen] = useState(false);

  const formatDate = (date: string) => {
    return format(new Date(date), 'MMM d, yyyy');
  };

  const formatCurrency = (amount: number) => {
    return `$${(amount / 100).toFixed(2)}`;
  };

  const getStatusBadge = () => {
    if (giftCard.isDisabled) {
      return <Badge variant="secondary">Disabled</Badge>;
    }
    if (giftCard.balance === 0) {
      return <Badge variant="outline">Depleted</Badge>;
    }
    if (giftCard.endsAt && isPast(new Date(giftCard.endsAt))) {
      return <Badge variant="destructive">Expired</Badge>;
    }
    return <Badge variant="default">Active</Badge>;
  };

  const getBalancePercentage = () => {
    if (giftCard.value === 0) return 0;
    return Math.round((giftCard.balance / giftCard.value) * 100);
  };

  return (
    <>
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm hover:shadow-md transition-shadow">
        <Accordion type="single" collapsible className="w-full">
          <AccordionItem value={giftCard.id} className="border-none">
            <AccordionTrigger className="hover:no-underline px-6 py-4">
              <div className="flex items-center justify-between w-full pr-4">
                <div className="flex items-center gap-4">
                  <div className="w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-full flex items-center justify-center text-white">
                    <Gift className="w-5 h-5" />
                  </div>
                  <div className="text-left">
                    <h3 className="font-semibold text-gray-900 dark:text-gray-100">
                      {giftCard.code}
                    </h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Balance: {formatCurrency(giftCard.balance)} / {formatCurrency(giftCard.value)}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-4">
                  {getStatusBadge()}
                  <div className="hidden sm:flex items-center gap-2">
                    <div className="w-24 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div 
                        className="bg-gradient-to-r from-orange-500 to-orange-600 h-2 rounded-full"
                        style={{ width: `${getBalancePercentage()}%` }}
                      />
                    </div>
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      {getBalancePercentage()}%
                    </span>
                  </div>
                </div>
              </div>
            </AccordionTrigger>
            <AccordionContent className="px-6 pb-4">
              <div className="space-y-4">
                <div className="flex flex-wrap gap-4 text-sm text-gray-600 dark:text-gray-400">
                  <div className="flex items-center gap-2">
                    <DollarSign className="w-4 h-4" />
                    Original Value: {formatCurrency(giftCard.value)}
                  </div>
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4" />
                    Created: {formatDate(giftCard.createdAt)}
                  </div>
                  {giftCard.endsAt && (
                    <div className="flex items-center gap-2">
                      <Calendar className="w-4 h-4" />
                      Expires: {formatDate(giftCard.endsAt)}
                    </div>
                  )}
                </div>

                {giftCard.region && (
                  <div className="flex items-center gap-2 text-sm">
                    <MapPin className="w-4 h-4 text-gray-500" />
                    <span className="text-gray-600 dark:text-gray-400">
                      Region: {giftCard.region.name}
                    </span>
                  </div>
                )}

                {giftCard.order && (
                  <div className="flex items-center gap-2 text-sm">
                    <Receipt className="w-4 h-4 text-gray-500" />
                    <span className="text-gray-600 dark:text-gray-400">
                      Purchased in order #{giftCard.order.orderNumber}
                    </span>
                  </div>
                )}

                {giftCard.giftCardTransactions && giftCard.giftCardTransactions.length > 0 && (
                  <div className="border-t pt-4">
                    <h4 className="font-medium text-sm mb-2 flex items-center gap-2">
                      <History className="w-4 h-4" />
                      Transaction History ({giftCard.giftCardTransactions.length})
                    </h4>
                    <div className="space-y-2">
                      {giftCard.giftCardTransactions.slice(0, 5).map((transaction) => (
                        <div key={transaction.id} className="flex justify-between text-sm">
                          <div className="text-gray-600 dark:text-gray-400">
                            {transaction.order ? `Order #${transaction.order.orderNumber}` : 'Direct transaction'}
                            <span className="text-xs ml-2 text-gray-500">
                              {formatDate(transaction.createdAt)}
                            </span>
                          </div>
                          <span className={`font-medium ${transaction.amount < 0 ? 'text-red-600' : 'text-green-600'}`}>
                            {transaction.amount < 0 ? '-' : '+'}{formatCurrency(Math.abs(transaction.amount))}
                          </span>
                        </div>
                      ))}
                      {giftCard.giftCardTransactions.length > 5 && (
                        <p className="text-sm text-gray-500">
                          +{giftCard.giftCardTransactions.length - 5} more transactions
                        </p>
                      )}
                    </div>
                  </div>
                )}

                <div className="flex justify-end pt-2">
                  <Button
                    onClick={() => setIsEditOpen(true)}
                    variant="outline"
                    size="sm"
                  >
                    Edit Gift Card
                  </Button>
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </div>

      <EditItemDrawer
        isOpen={isEditOpen}
        onClose={() => setIsEditOpen(false)}
        itemId={giftCard.id}
        listKey="GiftCard"
      />
    </>
  );
}