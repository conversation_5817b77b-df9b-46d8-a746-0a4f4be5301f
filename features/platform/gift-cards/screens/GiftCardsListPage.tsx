import { Suspense } from 'react';
import { PageBreadcrumbs } from '@/features/dashboard/components/PageBreadcrumbs';
import { PlatformFilterBar } from '@/features/dashboard/components/PlatformFilterBar';
import { PaginationWrapper } from '@/features/dashboard/components/PaginationWrapper';
import { GiftCardDetailsComponent } from '../components/GiftCardDetailsComponent';
import { StatusTabs } from '../components/StatusTabs';
import { getGiftCards, getGiftCardStatusCounts } from '../actions';
import { Triangle, Square, Circle } from 'lucide-react';

interface GiftCardsListPageProps {
  searchParams: {
    page?: string;
    search?: string;
    sortBy?: string;
    status?: string;
  };
}

async function GiftCardsContent({ searchParams }: GiftCardsListPageProps) {
  const page = parseInt(searchParams.page || '1');
  const search = searchParams.search || '';
  const sortBy = searchParams.sortBy || 'createdAt_DESC';
  const status = searchParams.status || 'all';

  const filters: any = {};
  if (status === 'active') {
    filters.isDisabled = false;
    filters.hasBalance = true;
  } else if (status === 'depleted') {
    filters.hasBalance = false;
  } else if (status === 'disabled') {
    filters.isDisabled = true;
  } else if (status === 'expired') {
    filters.hasExpired = true;
  }

  const [{ giftCards, totalCount }, statusCounts] = await Promise.all([
    getGiftCards({ page, search, sortBy, filters }),
    getGiftCardStatusCounts()
  ]);

  const totalPages = Math.ceil(totalCount / 20);

  return (
    <>
      <div className="mb-6">
        <StatusTabs
          counts={statusCounts}
          currentStatus={status}
          onStatusChange={() => {}}
        />
      </div>

      {giftCards.length === 0 ? (
        <div className="text-center py-12">
          <div className="flex justify-center items-center mb-4 space-x-2">
            <Triangle className="w-8 h-8 text-gray-300 animate-pulse" />
            <Square className="w-8 h-8 text-gray-300 animate-pulse delay-75" />
            <Circle className="w-8 h-8 text-gray-300 animate-pulse delay-150" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
            No gift cards found
          </h3>
          <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
            {search ? 'Try adjusting your search terms' : 'Create your first gift card to get started'}
          </p>
        </div>
      ) : (
        <>
          <div className="grid gap-4">
            {giftCards.map((giftCard) => (
              <GiftCardDetailsComponent key={giftCard.id} giftCard={giftCard} />
            ))}
          </div>

          <div className="mt-8">
            <PaginationWrapper
              currentPage={page}
              totalPages={totalPages}
              basePath="/platform/gift-cards"
            />
          </div>
        </>
      )}
    </>
  );
}

export default async function GiftCardsListPage({ searchParams }: GiftCardsListPageProps) {
  return (
    <div className="container mx-auto py-8">
      <PageBreadcrumbs
        items={[
          { label: 'Dashboard', href: '/dashboard' },
          { label: 'Platform', href: '/dashboard/platform' },
          { label: 'Gift Cards' }
        ]}
      />

      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
          Gift Cards
        </h1>
        <p className="mt-2 text-gray-600 dark:text-gray-400">
          Issue and manage gift cards for customer purchases and rewards.
        </p>
      </div>

      <div className="mb-6">
        <PlatformFilterBar
          searchPlaceholder="Search gift cards by code..."
          sortOptions={[
            { label: 'Newest First', value: 'createdAt_DESC' },
            { label: 'Oldest First', value: 'createdAt_ASC' },
            { label: 'Value (High to Low)', value: 'value_DESC' },
            { label: 'Value (Low to High)', value: 'value_ASC' },
            { label: 'Balance (High to Low)', value: 'balance_DESC' },
            { label: 'Balance (Low to High)', value: 'balance_ASC' }
          ]}
        />
      </div>

      <Suspense fallback={<div>Loading gift cards...</div>}>
        <GiftCardsContent searchParams={searchParams} />
      </Suspense>
    </div>
  );
}