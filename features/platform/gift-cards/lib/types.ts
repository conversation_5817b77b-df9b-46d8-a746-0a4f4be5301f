export interface GiftCard {
  id: string;
  code: string;
  value: number;
  balance: number;
  isDisabled: boolean;
  endsAt?: string | null;
  metadata?: any;
  order?: {
    id: string;
    orderNumber: string;
  } | null;
  region?: {
    id: string;
    name: string;
  } | null;
  giftCardTransactions?: Array<{
    id: string;
    amount: number;
    isTaxable: boolean;
    taxRate?: number | null;
    order?: {
      id: string;
      orderNumber: string;
    } | null;
    createdAt: string;
  }>;
  createdAt: string;
  updatedAt: string;
}

export interface GiftCardFilters {
  search?: string;
  isDisabled?: boolean;
  hasBalance?: boolean;
  hasExpired?: boolean;
}

export interface GiftCardStatusCounts {
  all: number;
  active: number;
  disabled: number;
  expired: number;
  depleted: number;
}