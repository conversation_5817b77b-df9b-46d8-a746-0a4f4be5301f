'use server';

import { keystoneClient } from '@/features/dashboard/lib/keystoneClient';
import { GiftCardFilters, GiftCardStatusCounts } from '../lib/types';

export async function getGiftCards({
  page = 1,
  perPage = 20,
  search = '',
  sortBy = 'createdAt_DESC',
  filters = {}
}: {
  page?: number;
  perPage?: number;
  search?: string;
  sortBy?: string;
  filters?: GiftCardFilters;
}) {
  const where: any = {};
  
  if (search) {
    where.code = { contains: search, mode: 'insensitive' };
  }
  
  if (filters.isDisabled !== undefined) {
    where.isDisabled = { equals: filters.isDisabled };
  }
  
  if (filters.hasBalance) {
    where.balance = { gt: 0 };
  }
  
  if (filters.hasExpired) {
    where.endsAt = { lt: new Date().toISOString() };
  }
  
  const query = `
    query GiftCardsQuery($where: GiftCardWhereInput, $orderBy: [GiftCardOrderByInput!], $take: Int, $skip: Int) {
      giftCards(where: $where, orderBy: $orderBy, take: $take, skip: $skip) {
        id
        code
        value
        balance
        isDisabled
        endsAt
        metadata
        order {
          id
          orderNumber
        }
        region {
          id
          name
        }
        giftCardTransactions {
          id
          amount
          isTaxable
          taxRate
          order {
            id
            orderNumber
          }
          createdAt
        }
        createdAt
        updatedAt
      }
      giftCardsCount(where: $where)
    }
  `;

  const response = await keystoneClient(query, {
    where,
    orderBy: [sortBy],
    take: perPage,
    skip: (page - 1) * perPage
  });
  
  if (!response.success) {
    throw new Error(response.error);
  }
  
  return {
    giftCards: response.data.giftCards || [],
    totalCount: response.data.giftCardsCount || 0
  };
}

export async function getGiftCardStatusCounts() {
  const now = new Date().toISOString();
  
  const query = `
    query GiftCardStatusCounts($now: DateTime!) {
      all: giftCardsCount
      active: giftCardsCount(where: { 
        isDisabled: { equals: false },
        balance: { gt: 0 },
        OR: [
          { endsAt: { gte: $now } },
          { endsAt: { equals: null } }
        ]
      })
      disabled: giftCardsCount(where: { isDisabled: { equals: true } })
      expired: giftCardsCount(where: { 
        endsAt: { lt: $now }
      })
      depleted: giftCardsCount(where: { balance: { equals: 0 } })
    }
  `;
  
  const response = await keystoneClient(query, { now });
  
  if (!response.success) {
    throw new Error(response.error);
  }
  
  return {
    all: response.data.all || 0,
    active: response.data.active || 0,
    disabled: response.data.disabled || 0,
    expired: response.data.expired || 0,
    depleted: response.data.depleted || 0
  };
}