import { PageBreadcrumbs } from '@/features/dashboard/components/PageBreadcrumbs';
import { Card } from '@/components/ui/card';
import { Settings, AlertCircle } from 'lucide-react';

export default async function SettingsPage() {
  return (
    <div className="container mx-auto py-8">
      <PageBreadcrumbs
        items={[
          { label: 'Dashboard', href: '/dashboard', type: 'link' },
          { label: 'Platform', href: '/dashboard/platform', type: 'link' },
          { label: 'Settings', type: 'page' }
        ]}
      />

      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
          Settings
        </h1>
        <p className="mt-2 text-gray-600 dark:text-gray-400">
          Configure platform settings, integrations, and system preferences.
        </p>
      </div>

      <Card className="p-12">
        <div className="text-center">
          <div className="w-16 h-16 bg-zinc-100 dark:bg-zinc-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <Settings className="w-8 h-8 text-zinc-600" />
          </div>
          <h2 className="text-xl font-semibold mb-2">Platform Settings</h2>
          <p className="text-gray-600 dark:text-gray-400 max-w-md mx-auto">
            The settings interface is being developed. This will allow you to configure store details, 
            manage integrations, set up payment methods, configure taxes, and manage other system preferences.
          </p>
          <div className="mt-6 flex items-center justify-center gap-2 text-sm text-amber-600">
            <AlertCircle className="w-4 h-4" />
            <span>Coming soon</span>
          </div>
        </div>
      </Card>
    </div>
  );
}