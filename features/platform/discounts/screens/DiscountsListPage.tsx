// import { PageBreadcrumbs } from '@/features/dashboard/components/PageBreadcrumbs';
// import { PlatformFilterBar } from '@/features/dashboard/components/PlatformFilterBar';
import { PaginationWrapper } from '@/features/dashboard/components/PaginationWrapper';
import { DiscountDetailsComponent } from '../components/DiscountDetailsComponent';
import { StatusTabs } from '../components/StatusTabs';
import { getFilteredDiscounts, getDiscountStatusCounts } from '../actions';
import { Triangle, Square, Circle } from 'lucide-react';
import Link from "next/link";
import { Button } from "@/components/ui/button";
import type { SortOption } from '@/features/dashboard/components/PlatformFilterBar';

interface PageProps {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}

interface Discount {
  id: string;
  code: string;
  isDynamic: boolean;
  isDisabled: boolean;
  stackable: boolean;
  startsAt?: string;
  endsAt?: string;
  metadata?: any;
  usageLimit?: number;
  usageCount?: number;
  validDuration?: string;
  discountRule?: {
    id: string;
    description?: string;
    type: string;
    value: number;
    allocation: string;
  };
  regions?: Array<{
    id: string;
    name: string;
  }>;
  createdAt: string;
  updatedAt?: string;
}

function ErrorDisplay({ title, message }: { title: string; message: string }) {
  return (
    <div className="px-4 sm:px-6 lg:px-8 py-8">
      <h1 className="text-2xl font-bold tracking-tight text-red-600">
        {title}
      </h1>
      <p className="mt-2 text-gray-600">{message}</p>
    </div>
  );
}

export async function DiscountsListPage({ searchParams }: PageProps) {
  const resolvedSearchParams = await searchParams;
  // Parse search parameters
  const page = Number(resolvedSearchParams.page) || 1;
  const pageSize = Number(resolvedSearchParams.pageSize) || 10;

  // Get status filter from URL
  let status = null;
  const statusFilter = resolvedSearchParams["!status_matches"];
  if (statusFilter) {
    try {
      const parsed = JSON.parse(decodeURIComponent(statusFilter as string));
      if (Array.isArray(parsed) && parsed.length > 0) {
        status = parsed[0].value;
      }
    } catch (e) {
      // Invalid JSON in URL, ignore
    }
  }

  // Get search term from URL
  const search = typeof resolvedSearchParams.search === "string" && resolvedSearchParams.search !== "" ? resolvedSearchParams.search : null;

  try {
    // Create minimal list config for discounts
    const list = {
      key: "Discount",
      path: "discounts",
      label: "Discount",
      singular: "discount",
      plural: "discounts",
      description: "Manage discount codes",
      labelField: "code",
      initialColumns: ["code", "status", "createdAt"],
      groups: [],
      graphql: {
        plural: "discounts",
        singular: "discount"
      },
      fields: {},
      gqlNames: {
        deleteMutationName: 'deleteDiscount',
        listQueryName: 'discounts',
        itemQueryName: 'discount',
        listQueryCountName: 'discountsCount',
        listOrderName: 'DiscountOrderByInput',
        updateMutationName: 'updateDiscount',
        createMutationName: 'createDiscount',
        whereInputName: 'DiscountWhereInput',
        whereUniqueInputName: 'DiscountWhereUniqueInput',
        updateInputName: 'DiscountUpdateInput',
        createInputName: 'DiscountCreateInput'
      }
    };

    // Get sort from URL
    const sortBy = resolvedSearchParams.sortBy as string | undefined;
    const sort = sortBy ? {
      field: sortBy.startsWith("-") ? sortBy.slice(1) : sortBy,
      direction: sortBy.startsWith("-") ? "DESC" : "ASC"
    } as SortOption : null;

    // Fetch discounts with filters
    const response = await getFilteredDiscounts(
      status,
      search,
      page,
      pageSize,
      sort
    );

    let discounts: Discount[] = [];
    let count = 0;

    if (response.success) {
      // Ensure data exists and has the expected properties
      discounts = response.data?.items || [];
      count = response.data?.count || 0;
    } else {
      // Log the error and use fallback values
      console.error("Error fetching discounts:", response.error);
    }

    // Get status counts
    const statusCountsResponse = await getDiscountStatusCounts();
    let statusCounts = {
      active: 0,
      disabled: 0,
      expired: 0,
      all: 0,
    };

    if (statusCountsResponse) {
      statusCounts = statusCountsResponse;
    } else {
      console.error("Error fetching discount status counts");
    }

    return (
      <div className="min-h-screen p-6">
        <div className="mb-6">
          <nav className="text-sm text-gray-600">
            <span>Dashboard</span> / <span>Platform</span> / <span className="text-gray-900">Discounts</span>
          </nav>
        </div>

        <div className="flex flex-col flex-1 min-h-0">

          <div className="border-gray-200 dark:border-gray-800">
            <div className="px-4 md:px-6 pt-4 md:pt-6 pb-4">
              <h1 className="text-2xl font-semibold text-gray-900 dark:text-gray-50">
                Discounts
              </h1>
              <p className="text-muted-foreground">
                <span>Manage your discount codes and promotions</span>
              </p>
            </div>
          </div>

          <div className="px-4 py-2 border-b">
            <div className="text-sm text-gray-600">
              Search and filter functionality will be added here
            </div>
          </div>

          <div className="border-b px-4 py-2">
            <div className="flex gap-4 text-sm text-gray-600">
              <span>All: {statusCounts.all}</span>
              <span>Active: {statusCounts.active}</span>
              <span>Disabled: {statusCounts.disabled}</span>
              <span>Expired: {statusCounts.expired}</span>
            </div>
          </div>

          <div className="flex-1 overflow-auto pb-4">
            {discounts && discounts.length > 0 ? (
              <div className="grid grid-cols-1 divide-y">
                {discounts.map((discount: Discount) => (
                  <DiscountDetailsComponent key={discount.id} discount={discount} />
                ))}
              </div>
            ) : (
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <div className="relative h-11 w-11 mx-auto mb-2">
                    <Triangle className="absolute left-1 top-1 w-4 h-4 fill-indigo-200 stroke-indigo-400 dark:stroke-indigo-600 dark:fill-indigo-950 rotate-[90deg]" />
                    <Square className="absolute right-[.2rem] top-1 w-4 h-4 fill-orange-300 stroke-orange-500 dark:stroke-amber-600 dark:fill-amber-950 rotate-[30deg]" />
                    <Circle className="absolute bottom-2 left-1/2 -translate-x-1/2 w-4 h-4 fill-emerald-200 stroke-emerald-400 dark:stroke-emerald-600 dark:fill-emerald-900" />
                  </div>
                  <p className="font-medium">No discounts found</p>
                  <p className="text-muted-foreground text-sm">
                    {(search !== null) || status
                      ? "Try adjusting your search or filter criteria"
                      : "Create your first discount to get started"}
                  </p>
                  {((search !== null) || status) && (
                    <Link href="/dashboard/platform/discounts">
                      <Button variant="outline" className="mt-4" size="sm">
                        Clear filters
                      </Button>
                    </Link>
                  )}
                </div>
              </div>
            )}
          </div>

          <PaginationWrapper
            currentPage={page}
            total={count}
            pageSize={pageSize}
            list={{
              singular: "discount",
              plural: "discounts",
              path: "discounts",
              gqlNames: {
                deleteMutationName: list.gqlNames?.deleteMutationName || '',
                listQueryName: list.gqlNames?.listQueryName || '',
                itemQueryName: list.gqlNames?.itemQueryName || '',
                listQueryCountName: list.gqlNames?.listQueryCountName || '',
                listOrderName: list.gqlNames?.listOrderName || '',
                updateMutationName: list.gqlNames?.updateMutationName || '',
                createMutationName: list.gqlNames?.createMutationName || '',
                whereInputName: list.gqlNames?.whereInputName || '',
                whereUniqueInputName: list.gqlNames?.whereUniqueInputName || '',
                updateInputName: list.gqlNames?.updateInputName || '',
                createInputName: list.gqlNames?.createInputName || ''
              }
            }}
          />
        </div>
      </div>
    );
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : "An unknown error occurred";
    return (
      <ErrorDisplay
        title="Error Loading Discounts"
        message={`There was an error loading discounts: ${errorMessage}`}
      />
    );
  }
}