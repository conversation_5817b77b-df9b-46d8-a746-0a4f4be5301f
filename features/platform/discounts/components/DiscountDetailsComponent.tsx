'use client';

import { useState } from 'react';
import { Discount } from '../lib/types';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { EditItemDrawer } from '@/features/dashboard/components/EditItemDrawer';
import { Tag, Percent, DollarSign, Truck, Calendar, Hash, MapPin, Package } from 'lucide-react';
import { format, isPast, isFuture } from 'date-fns';

interface DiscountDetailsComponentProps {
  discount: Discount;
}

export function DiscountDetailsComponent({ discount }: DiscountDetailsComponentProps) {
  const [isEditOpen, setIsEditOpen] = useState(false);

  const formatDate = (date: string) => {
    return format(new Date(date), 'MMM d, yyyy');
  };

  const getStatusBadge = () => {
    if (discount.isDisabled) {
      return <Badge variant="secondary">Disabled</Badge>;
    }
    if (discount.endsAt && isPast(new Date(discount.endsAt))) {
      return <Badge variant="destructive">Expired</Badge>;
    }
    if (isFuture(new Date(discount.startsAt))) {
      return <Badge variant="outline">Scheduled</Badge>;
    }
    return <Badge variant="default">Active</Badge>;
  };

  const getTypeIcon = () => {
    switch (discount.discountRule?.type) {
      case 'percentage':
        return <Percent className="w-4 h-4" />;
      case 'fixed':
        return <DollarSign className="w-4 h-4" />;
      case 'free_shipping':
        return <Truck className="w-4 h-4" />;
      default:
        return <Tag className="w-4 h-4" />;
    }
  };

  const getDiscountValue = () => {
    if (!discount.discountRule) return 'N/A';
    
    switch (discount.discountRule.type) {
      case 'percentage':
        return `${discount.discountRule.value}% off`;
      case 'fixed':
        return `$${(discount.discountRule.value / 100).toFixed(2)} off`;
      case 'free_shipping':
        return 'Free shipping';
      default:
        return 'N/A';
    }
  };

  const getUsageInfo = () => {
    if (discount.usageLimit) {
      return `${discount.usageCount} / ${discount.usageLimit} used`;
    }
    return `${discount.usageCount} used`;
  };

  return (
    <>
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm hover:shadow-md transition-shadow">
        <Accordion type="single" collapsible className="w-full">
          <AccordionItem value={discount.id} className="border-none">
            <AccordionTrigger className="hover:no-underline px-6 py-4">
              <div className="flex items-center justify-between w-full pr-4">
                <div className="flex items-center gap-4">
                  <div className="w-10 h-10 bg-gradient-to-br from-pink-500 to-pink-600 rounded-full flex items-center justify-center text-white">
                    {getTypeIcon()}
                  </div>
                  <div className="text-left">
                    <h3 className="font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
                      {discount.code}
                      {discount.isDynamic && (
                        <Badge variant="outline" className="text-xs">
                          Dynamic
                        </Badge>
                      )}
                    </h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {getDiscountValue()} • {getUsageInfo()}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-4">
                  {getStatusBadge()}
                  {discount.stackable && (
                    <Badge variant="outline" className="hidden sm:inline-flex">
                      Stackable
                    </Badge>
                  )}
                </div>
              </div>
            </AccordionTrigger>
            <AccordionContent className="px-6 pb-4">
              <div className="space-y-4">
                {discount.discountRule?.description && (
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {discount.discountRule.description}
                  </p>
                )}

                <div className="flex flex-wrap gap-4 text-sm text-gray-600 dark:text-gray-400">
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4" />
                    Valid: {formatDate(discount.startsAt)}
                    {discount.endsAt && ` - ${formatDate(discount.endsAt)}`}
                  </div>
                  {discount.validDuration && (
                    <div className="flex items-center gap-2">
                      <Hash className="w-4 h-4" />
                      Duration: {discount.validDuration}
                    </div>
                  )}
                </div>

                {discount.regions && discount.regions.length > 0 && (
                  <div className="border-t pt-4">
                    <h4 className="font-medium text-sm mb-2 flex items-center gap-2">
                      <MapPin className="w-4 h-4" />
                      Available Regions ({discount.regions.length})
                    </h4>
                    <div className="flex flex-wrap gap-2">
                      {discount.regions.map((region) => (
                        <Badge key={region.id} variant="secondary">
                          {region.name}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {discount.discountRule?.products && discount.discountRule.products.length > 0 && (
                  <div className="border-t pt-4">
                    <h4 className="font-medium text-sm mb-2 flex items-center gap-2">
                      <Package className="w-4 h-4" />
                      Applicable Products ({discount.discountRule.products.length})
                    </h4>
                    <div className="space-y-1">
                      {discount.discountRule.products.slice(0, 3).map((product) => (
                        <p key={product.id} className="text-sm text-gray-600 dark:text-gray-400">
                          {product.title}
                        </p>
                      ))}
                      {discount.discountRule.products.length > 3 && (
                        <p className="text-sm text-gray-500">
                          +{discount.discountRule.products.length - 3} more products
                        </p>
                      )}
                    </div>
                  </div>
                )}

                {discount.discountRule?.discountConditions && discount.discountRule.discountConditions.length > 0 && (
                  <div className="border-t pt-4">
                    <h4 className="font-medium text-sm mb-2">Conditions</h4>
                    <div className="space-y-1">
                      {discount.discountRule.discountConditions.map((condition) => (
                        <Badge key={condition.id} variant="outline" className="text-xs">
                          {condition.type} {condition.operator}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                <div className="flex justify-end pt-2">
                  <Button
                    onClick={() => setIsEditOpen(true)}
                    variant="outline"
                    size="sm"
                  >
                    Edit Discount
                  </Button>
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </div>

      <EditItemDrawer
        isOpen={isEditOpen}
        onClose={() => setIsEditOpen(false)}
        itemId={discount.id}
        listKey="Discount"
      />
    </>
  );
}