'use client';

import { Ta<PERSON>, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { DiscountStatusCounts } from '../lib/types';

interface StatusTabsProps {
  counts: DiscountStatusCounts;
  currentStatus: string;
  onStatusChange: (status: string) => void;
}

export function StatusTabs({ counts, currentStatus, onStatusChange }: StatusTabsProps) {
  return (
    <Tabs value={currentStatus} onValueChange={onStatusChange}>
      <TabsList>
        <TabsTrigger value="all">
          All Discounts
          <span className="ml-2 text-xs bg-gray-100 dark:bg-gray-800 px-2 py-0.5 rounded-full">
            {counts.all}
          </span>
        </TabsTrigger>
        <TabsTrigger value="active">
          Active
          <span className="ml-2 text-xs bg-gray-100 dark:bg-gray-800 px-2 py-0.5 rounded-full">
            {counts.active}
          </span>
        </TabsTrigger>
        <TabsTrigger value="disabled">
          Disabled
          <span className="ml-2 text-xs bg-gray-100 dark:bg-gray-800 px-2 py-0.5 rounded-full">
            {counts.disabled}
          </span>
        </TabsTrigger>
        <TabsTrigger value="expired">
          Expired
          <span className="ml-2 text-xs bg-gray-100 dark:bg-gray-800 px-2 py-0.5 rounded-full">
            {counts.expired}
          </span>
        </TabsTrigger>
      </TabsList>
    </Tabs>
  );
}