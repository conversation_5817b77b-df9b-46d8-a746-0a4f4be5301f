'use server';

import { keystoneClient } from '@/features/dashboard/lib/keystoneClient';

/**
 * Get list of discounts
 */
export async function getDiscounts(
  where: Record<string, unknown> = {},
  take: number = 10,
  skip: number = 0,
  orderBy: Array<Record<string, string>> = [{ createdAt: 'desc' }],
  selectedFields: string = `
    id
    code
    isDynamic
    isDisabled
    stackable
    startsAt
    endsAt
    metadata
    usageLimit
    usageCount
    validDuration
    discountRule {
      id
      description
      type
      value
      allocation
    }
    regions {
      id
      name
    }
    createdAt
    updatedAt
  `
) {
  const query = `
    query GetDiscounts($where: DiscountWhereInput, $take: Int!, $skip: Int!, $orderBy: [DiscountOrderByInput!]) {
      items: discounts(where: $where, take: $take, skip: $skip, orderBy: $orderBy) {
        ${selectedFields}
      }
      count: discountsCount(where: $where)
    }
  `;

  const response = await keystoneClient(query, { where, take, skip, orderBy });
  return response;
}

/**
 * Get filtered discounts based on status and search parameters
 */
export async function getFilteredDiscounts(
  status: string | null = null,
  search: string | null = null,
  page: number = 1,
  pageSize: number = 10,
  sort: { field: string; direction: 'ASC' | 'DESC' } | null = null
) {
  const where: Record<string, unknown> = {};

  // Add status filter if provided and not 'all'
  if (status && status !== 'all') {
    if (status === 'active') {
      const now = new Date().toISOString();
      where.AND = [
        { isDisabled: { equals: false } },
        { startsAt: { lte: now } },
        {
          OR: [
            { endsAt: { gte: now } },
            { endsAt: { equals: null } }
          ]
        }
      ];
    } else if (status === 'disabled') {
      where.isDisabled = { equals: true };
    } else if (status === 'expired') {
      where.endsAt = { lt: new Date().toISOString() };
    }
  }

  // Add search filter if provided
  if (search) {
    where.OR = [
      { code: { contains: search, mode: 'insensitive' } },
      { discountRule: { description: { contains: search, mode: 'insensitive' } } },
    ];
  }

  // Calculate pagination
  const skip = (page - 1) * pageSize;

  // Handle sorting
  const orderBy = sort
    ? [{ [sort.field]: sort.direction.toLowerCase() }]
    : [{ createdAt: 'desc' }];

  return getDiscounts(where, pageSize, skip, orderBy);
}

export async function getDiscountStatusCounts() {
  const now = new Date().toISOString();
  
  const query = `
    query DiscountStatusCounts($now: DateTime!) {
      all: discountsCount
      active: discountsCount(where: { 
        isDisabled: { equals: false },
        startsAt: { lte: $now },
        OR: [
          { endsAt: { gte: $now } },
          { endsAt: { equals: null } }
        ]
      })
      disabled: discountsCount(where: { isDisabled: { equals: true } })
      expired: discountsCount(where: { 
        endsAt: { lt: $now }
      })
    }
  `;
  
  const response = await keystoneClient(query, { now });
  
  if (!response.success) {
    throw new Error(response.error);
  }
  
  return {
    all: response.data.all || 0,
    active: response.data.active || 0,
    disabled: response.data.disabled || 0,
    expired: response.data.expired || 0
  };
}