'use server';

import { keystoneClient } from '@/features/dashboard/lib/keystoneClient';
import { ReturnFilters, ReturnStatusCounts } from '../lib/types';

export async function getReturns({
  page = 1,
  perPage = 20,
  search = '',
  sortBy = 'createdAt_DESC',
  filters = {}
}: {
  page?: number;
  perPage?: number;
  search?: string;
  sortBy?: string;
  filters?: ReturnFilters;
}) {
  const where: any = {};
  
  if (search) {
    where.OR = [
      { order: { orderNumber: { contains: search, mode: 'insensitive' } } },
      { idempotencyKey: { contains: search, mode: 'insensitive' } }
    ];
  }
  
  if (filters.status) {
    where.status = { equals: filters.status };
  }
  
  const query = `
    query ReturnsQuery($where: ReturnWhereInput, $orderBy: [ReturnOrderByInput!], $take: Int, $skip: Int) {
      returns(where: $where, orderBy: $orderBy, take: $take, skip: $skip) {
        id
        status
        shippingData
        refundAmount
        receivedAt
        metadata
        idempotencyKey
        noNotification
        claimOrder {
          id
          type
        }
        swap {
          id
        }
        order {
          id
          orderNumber
          total
        }
        returnItems {
          id
          quantity
          isRequested
          requestedQuantity
          receivedQuantity
          note
          lineItem {
            id
            title
            variant {
              id
              title
            }
          }
          returnReason {
            id
            label
            value
          }
        }
        shippingMethod {
          id
          shippingOption {
            id
            name
          }
        }
        createdAt
        updatedAt
      }
      returnsCount(where: $where)
    }
  `;

  const response = await keystoneClient(query, {
    where,
    orderBy: [sortBy],
    take: perPage,
    skip: (page - 1) * perPage
  });
  
  if (!response.success) {
    throw new Error(response.error);
  }
  
  return {
    returns: response.data.returns || [],
    totalCount: response.data.returnsCount || 0
  };
}

export async function getReturnStatusCounts() {
  const query = `
    query ReturnStatusCounts {
      all: returnsCount
      requested: returnsCount(where: { status: { equals: requested } })
      received: returnsCount(where: { status: { equals: received } })
      requiresAction: returnsCount(where: { status: { equals: requires_action } })
      canceled: returnsCount(where: { status: { equals: canceled } })
    }
  `;
  
  const response = await keystoneClient(query);
  
  if (!response.success) {
    throw new Error(response.error);
  }
  
  return {
    all: response.data.all || 0,
    requested: response.data.requested || 0,
    received: response.data.received || 0,
    requiresAction: response.data.requiresAction || 0,
    canceled: response.data.canceled || 0
  };
}