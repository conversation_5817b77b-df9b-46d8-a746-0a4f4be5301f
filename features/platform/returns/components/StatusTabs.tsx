'use client';

import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ReturnStatusCounts } from '../lib/types';

interface StatusTabsProps {
  counts: ReturnStatusCounts;
  currentStatus: string;
  onStatusChange: (status: string) => void;
}

export function StatusTabs({ counts, currentStatus, onStatusChange }: StatusTabsProps) {
  return (
    <Tabs value={currentStatus} onValueChange={onStatusChange}>
      <TabsList>
        <TabsTrigger value="all">
          All Returns
          <span className="ml-2 text-xs bg-gray-100 dark:bg-gray-800 px-2 py-0.5 rounded-full">
            {counts.all}
          </span>
        </TabsTrigger>
        <TabsTrigger value="requested">
          Requested
          <span className="ml-2 text-xs bg-gray-100 dark:bg-gray-800 px-2 py-0.5 rounded-full">
            {counts.requested}
          </span>
        </TabsTrigger>
        <TabsTrigger value="received">
          Received
          <span className="ml-2 text-xs bg-gray-100 dark:bg-gray-800 px-2 py-0.5 rounded-full">
            {counts.received}
          </span>
        </TabsTrigger>
        <TabsTrigger value="requiresAction">
          Requires Action
          <span className="ml-2 text-xs bg-gray-100 dark:bg-gray-800 px-2 py-0.5 rounded-full">
            {counts.requiresAction}
          </span>
        </TabsTrigger>
        <TabsTrigger value="canceled">
          Canceled
          <span className="ml-2 text-xs bg-gray-100 dark:bg-gray-800 px-2 py-0.5 rounded-full">
            {counts.canceled}
          </span>
        </TabsTrigger>
      </TabsList>
    </Tabs>
  );
}