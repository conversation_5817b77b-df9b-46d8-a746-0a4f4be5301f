'use client';

import { useState } from 'react';
import { Return } from '../lib/types';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { EditItemDrawer } from '@/features/dashboard/components/EditItemDrawer';
import { 
  RotateCcw, Package, DollarSign, Calendar, 
  Truck, AlertCircle, CheckCircle, XCircle, FileText
} from 'lucide-react';
import { format } from 'date-fns';

interface ReturnDetailsComponentProps {
  return: Return;
}

export function ReturnDetailsComponent({ return: returnItem }: ReturnDetailsComponentProps) {
  const [isEditOpen, setIsEditOpen] = useState(false);

  const formatDate = (date: string) => {
    return format(new Date(date), 'MMM d, yyyy');
  };

  const formatCurrency = (amount: number) => {
    return `$${(amount / 100).toFixed(2)}`;
  };

  const getStatusBadge = () => {
    switch (returnItem.status) {
      case 'requested':
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">Requested</Badge>;
      case 'received':
        return <Badge variant="default" className="bg-green-600">Received</Badge>;
      case 'requires_action':
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">Requires Action</Badge>;
      case 'canceled':
        return <Badge variant="secondary">Canceled</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const getStatusIcon = () => {
    switch (returnItem.status) {
      case 'requested':
        return <RotateCcw className="w-4 h-4" />;
      case 'received':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'requires_action':
        return <AlertCircle className="w-4 h-4 text-yellow-600" />;
      case 'canceled':
        return <XCircle className="w-4 h-4 text-gray-600" />;
      default:
        return null;
    }
  };

  const getTotalItems = () => {
    if (!returnItem.returnItems) return 0;
    return returnItem.returnItems.reduce((sum, item) => sum + (item.quantity || 0), 0);
  };

  return (
    <>
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm hover:shadow-md transition-shadow">
        <Accordion type="single" collapsible className="w-full">
          <AccordionItem value={returnItem.id} className="border-none">
            <AccordionTrigger className="hover:no-underline px-6 py-4">
              <div className="flex items-center justify-between w-full pr-4">
                <div className="flex items-center gap-4">
                  <div className="w-10 h-10 bg-gradient-to-br from-red-500 to-red-600 rounded-full flex items-center justify-center text-white">
                    <RotateCcw className="w-5 h-5" />
                  </div>
                  <div className="text-left">
                    <h3 className="font-semibold text-gray-900 dark:text-gray-100">
                      Return for Order #{returnItem.order?.orderNumber || 'N/A'}
                    </h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {getTotalItems()} items • {returnItem.refundAmount ? formatCurrency(returnItem.refundAmount) : 'No refund amount'}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-4">
                  {getStatusBadge()}
                  {returnItem.claimOrder && (
                    <Badge variant="outline" className="hidden sm:inline-flex">
                      Claim
                    </Badge>
                  )}
                </div>
              </div>
            </AccordionTrigger>
            <AccordionContent className="px-6 pb-4">
              <div className="space-y-4">
                <div className="flex flex-wrap gap-4 text-sm text-gray-600 dark:text-gray-400">
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4" />
                    Requested: {formatDate(returnItem.createdAt)}
                  </div>
                  {returnItem.receivedAt && (
                    <div className="flex items-center gap-2">
                      <Calendar className="w-4 h-4" />
                      Received: {formatDate(returnItem.receivedAt)}
                    </div>
                  )}
                  <div className="flex items-center gap-2">
                    {getStatusIcon()}
                    Status: {returnItem.status?.replace('_', ' ') || 'unknown'}
                  </div>
                </div>

                {returnItem.order && (
                  <div className="border-t pt-4">
                    <h4 className="font-medium text-sm mb-2 flex items-center gap-2">
                      <FileText className="w-4 h-4" />
                      Order Details
                    </h4>
                    <div className="space-y-1 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Order Number</span>
                        <span>#{returnItem.order.orderNumber}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Order Total</span>
                        <span>{formatCurrency(returnItem.order.total)}</span>
                      </div>
                      {returnItem.refundAmount && (
                        <div className="flex justify-between font-medium">
                          <span>Refund Amount</span>
                          <span className="text-green-600">{formatCurrency(returnItem.refundAmount)}</span>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {returnItem.returnItems && returnItem.returnItems.length > 0 && (
                  <div className="border-t pt-4">
                    <h4 className="font-medium text-sm mb-2 flex items-center gap-2">
                      <Package className="w-4 h-4" />
                      Return Items ({returnItem.returnItems.length})
                    </h4>
                    <div className="space-y-3">
                      {returnItem.returnItems.map((item) => (
                        <div key={item.id} className="bg-gray-50 dark:bg-gray-900 p-3 rounded text-sm">
                          <div className="flex justify-between mb-1">
                            <span className="font-medium">
                              {item.lineItem?.title || 'Unknown Item'}
                              {item.lineItem?.variant && (
                                <span className="text-gray-500 ml-2">
                                  ({item.lineItem.variant.title})
                                </span>
                              )}
                            </span>
                            <span>Qty: {item.quantity}</span>
                          </div>
                          {item.returnReason && (
                            <p className="text-gray-600 dark:text-gray-400">
                              Reason: {item.returnReason.label || item.returnReason.value}
                            </p>
                          )}
                          {item.note && (
                            <p className="text-gray-600 dark:text-gray-400 italic">
                              Note: {item.note}
                            </p>
                          )}
                          {(item.requestedQuantity !== null || item.receivedQuantity !== null) && (
                            <div className="flex gap-4 mt-1 text-xs">
                              {item.requestedQuantity !== null && (
                                <span>Requested: {item.requestedQuantity}</span>
                              )}
                              {item.receivedQuantity !== null && (
                                <span>Received: {item.receivedQuantity}</span>
                              )}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {returnItem.shippingMethod && (
                  <div className="border-t pt-4">
                    <h4 className="font-medium text-sm mb-2 flex items-center gap-2">
                      <Truck className="w-4 h-4" />
                      Shipping Method
                    </h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {returnItem.shippingMethod.shippingOption?.name || 'Standard Shipping'}
                    </p>
                  </div>
                )}

                <div className="flex justify-end pt-2">
                  <Button
                    onClick={() => setIsEditOpen(true)}
                    variant="outline"
                    size="sm"
                  >
                    Manage Return
                  </Button>
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </div>

      <EditItemDrawer
        isOpen={isEditOpen}
        onClose={() => setIsEditOpen(false)}
        itemId={returnItem.id}
        listKey="Return"
      />
    </>
  );
}