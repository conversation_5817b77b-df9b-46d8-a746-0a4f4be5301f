export interface Return {
  id: string;
  status?: 'requested' | 'received' | 'requires_action' | 'canceled' | null;
  shippingData?: any;
  refundAmount?: number | null;
  receivedAt?: string | null;
  metadata?: any;
  idempotencyKey?: string | null;
  noNotification?: boolean | null;
  claimOrder?: {
    id: string;
    type?: string | null;
  } | null;
  swap?: {
    id: string;
  } | null;
  order?: {
    id: string;
    orderNumber: string;
    total: number;
  } | null;
  returnItems?: Array<{
    id: string;
    quantity: number;
    isRequested: boolean;
    requestedQuantity?: number | null;
    receivedQuantity?: number | null;
    note?: string | null;
    lineItem?: {
      id: string;
      title: string;
      variant?: {
        id: string;
        title: string;
      } | null;
    } | null;
    returnReason?: {
      id: string;
      label?: string | null;
      value?: string | null;
    } | null;
  }>;
  shippingMethod?: {
    id: string;
    shippingOption?: {
      id: string;
      name: string;
    } | null;
  } | null;
  createdAt: string;
  updatedAt: string;
}

export interface ReturnFilters {
  search?: string;
  status?: string;
}

export interface ReturnStatusCounts {
  all: number;
  requested: number;
  received: number;
  requiresAction: number;
  canceled: number;
}