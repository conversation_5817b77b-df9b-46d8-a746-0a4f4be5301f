import { PageBreadcrumbs } from '@/features/dashboard/components/PageBreadcrumbs';
import { Card } from '@/components/ui/card';
import { Truck, AlertCircle } from 'lucide-react';

export default async function ShippingPage() {
  return (
    <div className="container mx-auto py-8">
      <PageBreadcrumbs
        items={[
          { label: 'Dashboard', href: '/dashboard', type: 'link' },
          { label: 'Platform', href: '/dashboard/platform', type: 'link' },
          { label: 'Shipping', type: 'page' }
        ]}
      />

      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
          Shipping
        </h1>
        <p className="mt-2 text-gray-600 dark:text-gray-400">
          Configure shipping options, rates, and fulfillment methods.
        </p>
      </div>

      <Card className="p-12">
        <div className="text-center">
          <div className="w-16 h-16 bg-sky-100 dark:bg-sky-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <Truck className="w-8 h-8 text-sky-600" />
          </div>
          <h2 className="text-xl font-semibold mb-2">Shipping Configuration</h2>
          <p className="text-gray-600 dark:text-gray-400 max-w-md mx-auto">
            The shipping configuration interface is being developed. This will allow you to set up shipping zones, 
            configure rates, integrate with shipping providers, and manage fulfillment options.
          </p>
          <div className="mt-6 flex items-center justify-center gap-2 text-sm text-amber-600">
            <AlertCircle className="w-4 h-4" />
            <span>Coming soon</span>
          </div>
        </div>
      </Card>
    </div>
  );
}