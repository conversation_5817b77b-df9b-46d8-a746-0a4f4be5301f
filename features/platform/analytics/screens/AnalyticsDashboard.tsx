import { PageBreadcrumbs } from '@/features/dashboard/components/PageBreadcrumbs';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Bar<PERSON>hart, TrendingUp, ShoppingCart, Users, 
  DollarSign, Package, Percent, Activity 
} from 'lucide-react';

export default async function AnalyticsDashboard() {
  return (
    <div className="container mx-auto py-8">
      <PageBreadcrumbs
        items={[
          { label: 'Dashboard', href: '/dashboard', type: 'link' },
          { label: 'Platform', href: '/dashboard/platform', type: 'link' },
          { label: 'Analytics', type: 'page' }
        ]}
      />

      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
          Analytics
        </h1>
        <p className="mt-2 text-gray-600 dark:text-gray-400">
          View sales reports, customer insights, and business performance metrics.
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4 mb-8">
        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Total Revenue</p>
              <p className="text-2xl font-bold mt-1">$125,430</p>
              <p className="text-sm text-green-600 mt-2 flex items-center gap-1">
                <TrendingUp className="w-4 h-4" />
                +12.5% from last month
              </p>
            </div>
            <div className="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center">
              <DollarSign className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Total Orders</p>
              <p className="text-2xl font-bold mt-1">3,456</p>
              <p className="text-sm text-blue-600 mt-2 flex items-center gap-1">
                <TrendingUp className="w-4 h-4" />
                +8.2% from last month
              </p>
            </div>
            <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
              <ShoppingCart className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Active Customers</p>
              <p className="text-2xl font-bold mt-1">12,543</p>
              <p className="text-sm text-purple-600 mt-2 flex items-center gap-1">
                <TrendingUp className="w-4 h-4" />
                +15.3% from last month
              </p>
            </div>
            <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/20 rounded-full flex items-center justify-center">
              <Users className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Conversion Rate</p>
              <p className="text-2xl font-bold mt-1">3.24%</p>
              <p className="text-sm text-orange-600 mt-2 flex items-center gap-1">
                <TrendingUp className="w-4 h-4" />
                +0.5% from last month
              </p>
            </div>
            <div className="w-12 h-12 bg-orange-100 dark:bg-orange-900/20 rounded-full flex items-center justify-center">
              <Percent className="w-6 h-6 text-orange-600" />
            </div>
          </div>
        </Card>
      </div>

      <div className="grid gap-6 lg:grid-cols-2">
        <Card className="p-6">
          <h2 className="text-lg font-semibold mb-4 flex items-center gap-2">
            <BarChart className="w-5 h-5" />
            Sales Overview
          </h2>
          <div className="h-64 flex items-center justify-center text-gray-400">
            <div className="text-center">
              <BarChart className="w-12 h-12 mx-auto mb-2" />
              <p>Sales chart visualization would go here</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <h2 className="text-lg font-semibold mb-4 flex items-center gap-2">
            <Activity className="w-5 h-5" />
            Recent Activity
          </h2>
          <div className="space-y-4">
            <div className="flex items-center justify-between py-2 border-b">
              <div>
                <p className="font-medium">New order #1234</p>
                <p className="text-sm text-gray-500">2 minutes ago</p>
              </div>
              <span className="text-green-600 font-medium">$125.00</span>
            </div>
            <div className="flex items-center justify-between py-2 border-b">
              <div>
                <p className="font-medium">Customer registration</p>
                <p className="text-sm text-gray-500">15 minutes ago</p>
              </div>
              <Badge className="bg-blue-100 text-blue-700">New User</Badge>
            </div>
            <div className="flex items-center justify-between py-2 border-b">
              <div>
                <p className="font-medium">Product review</p>
                <p className="text-sm text-gray-500">1 hour ago</p>
              </div>
              <span className="text-yellow-600">★★★★★</span>
            </div>
          </div>
        </Card>
      </div>

      <div className="mt-6">
        <Card className="p-6">
          <h2 className="text-lg font-semibold mb-4 flex items-center gap-2">
            <Package className="w-5 h-5" />
            Top Products
          </h2>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-3 px-4">Product</th>
                  <th className="text-right py-3 px-4">Units Sold</th>
                  <th className="text-right py-3 px-4">Revenue</th>
                  <th className="text-right py-3 px-4">Growth</th>
                </tr>
              </thead>
              <tbody>
                <tr className="border-b">
                  <td className="py-3 px-4">Premium T-Shirt</td>
                  <td className="text-right py-3 px-4">543</td>
                  <td className="text-right py-3 px-4">$21,720</td>
                  <td className="text-right py-3 px-4 text-green-600">+12%</td>
                </tr>
                <tr className="border-b">
                  <td className="py-3 px-4">Wireless Headphones</td>
                  <td className="text-right py-3 px-4">321</td>
                  <td className="text-right py-3 px-4">$48,150</td>
                  <td className="text-right py-3 px-4 text-green-600">+8%</td>
                </tr>
                <tr className="border-b">
                  <td className="py-3 px-4">Smart Watch</td>
                  <td className="text-right py-3 px-4">234</td>
                  <td className="text-right py-3 px-4">$58,500</td>
                  <td className="text-right py-3 px-4 text-red-600">-3%</td>
                </tr>
              </tbody>
            </table>
          </div>
        </Card>
      </div>
    </div>
  );
}