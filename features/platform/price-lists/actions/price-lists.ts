'use server';

import { keystoneClient } from '@/features/dashboard/lib/keystoneClient';
import { PriceListFilters, PriceListStatusCounts } from '../lib/types';

export async function getPriceLists({
  page = 1,
  perPage = 20,
  search = '',
  sortBy = 'createdAt_DESC',
  filters = {}
}: {
  page?: number;
  perPage?: number;
  search?: string;
  sortBy?: string;
  filters?: PriceListFilters;
}) {
  const where: any = {};
  
  if (search) {
    where.OR = [
      { name: { contains: search, mode: 'insensitive' } },
      { description: { contains: search, mode: 'insensitive' } }
    ];
  }
  
  if (filters.type) {
    where.type = { equals: filters.type };
  }
  
  if (filters.status) {
    where.status = { equals: filters.status };
  }
  
  const query = `
    query PriceListsQuery($where: PriceListWhereInput, $orderBy: [PriceListOrderByInput!], $take: Int, $skip: Int) {
      priceLists(where: $where, orderBy: $orderBy, take: $take, skip: $skip) {
        id
        name
        description
        type
        status
        startsAt
        endsAt
        moneyAmounts {
          id
          amount
          currencyCode
          variant {
            id
            title
            product {
              id
              title
            }
          }
        }
        customerGroups {
          id
          name
        }
        createdAt
        updatedAt
      }
      priceListsCount(where: $where)
    }
  `;

  const response = await keystoneClient(query, {
    where,
    orderBy: [sortBy],
    take: perPage,
    skip: (page - 1) * perPage
  });
  
  if (!response.success) {
    throw new Error(response.error);
  }
  
  return {
    priceLists: response.data.priceLists || [],
    totalCount: response.data.priceListsCount || 0
  };
}

export async function getPriceListStatusCounts() {
  const now = new Date().toISOString();
  
  const query = `
    query PriceListStatusCounts($now: DateTime!) {
      all: priceListsCount
      active: priceListsCount(where: { status: { equals: active } })
      draft: priceListsCount(where: { status: { equals: draft } })
      expired: priceListsCount(where: { 
        endsAt: { lt: $now }
      })
    }
  `;
  
  const response = await keystoneClient(query, { now });
  
  if (!response.success) {
    throw new Error(response.error);
  }
  
  return {
    all: response.data.all || 0,
    active: response.data.active || 0,
    draft: response.data.draft || 0,
    expired: response.data.expired || 0
  };
}