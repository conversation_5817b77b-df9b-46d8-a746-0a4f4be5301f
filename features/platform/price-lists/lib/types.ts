export interface PriceList {
  id: string;
  name: string;
  description?: string | null;
  type: 'sale' | 'override';
  status: 'active' | 'draft';
  startsAt?: string | null;
  endsAt?: string | null;
  moneyAmounts?: Array<{
    id: string;
    amount: number;
    currencyCode: string;
    variant?: {
      id: string;
      title: string;
      product?: {
        id: string;
        title: string;
      } | null;
    } | null;
  }>;
  customerGroups?: Array<{
    id: string;
    name: string;
  }>;
  createdAt: string;
  updatedAt: string;
}

export interface PriceListFilters {
  search?: string;
  type?: string;
  status?: string;
}

export interface PriceListStatusCounts {
  all: number;
  active: number;
  draft: number;
  expired: number;
}