'use client';

import { useState } from 'react';
import { PriceList } from '../lib/types';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { EditItemDrawer } from '@/features/dashboard/components/EditItemDrawer';
import { DollarSign, Calendar, Users, Package, Tag } from 'lucide-react';
import { format, isPast, isFuture } from 'date-fns';

interface PriceListDetailsComponentProps {
  priceList: PriceList;
}

export function PriceListDetailsComponent({ priceList }: PriceListDetailsComponentProps) {
  const [isEditOpen, setIsEditOpen] = useState(false);

  const formatDate = (date: string) => {
    return format(new Date(date), 'MMM d, yyyy');
  };

  const formatCurrency = (amount: number, currencyCode: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currencyCode,
    }).format(amount / 100);
  };

  const getStatusBadge = () => {
    if (priceList.status === 'draft') {
      return <Badge variant="secondary">Draft</Badge>;
    }
    if (priceList.endsAt && isPast(new Date(priceList.endsAt))) {
      return <Badge variant="destructive">Expired</Badge>;
    }
    if (priceList.startsAt && isFuture(new Date(priceList.startsAt))) {
      return <Badge variant="outline">Scheduled</Badge>;
    }
    return <Badge variant="default">Active</Badge>;
  };

  const getTypeBadge = () => {
    switch (priceList.type) {
      case 'sale':
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Sale</Badge>;
      case 'override':
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">Override</Badge>;
      default:
        return null;
    }
  };

  return (
    <>
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm hover:shadow-md transition-shadow">
        <Accordion type="single" collapsible className="w-full">
          <AccordionItem value={priceList.id} className="border-none">
            <AccordionTrigger className="hover:no-underline px-6 py-4">
              <div className="flex items-center justify-between w-full pr-4">
                <div className="flex items-center gap-4">
                  <div className="w-10 h-10 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-full flex items-center justify-center text-white">
                    <Tag className="w-5 h-5" />
                  </div>
                  <div className="text-left">
                    <h3 className="font-semibold text-gray-900 dark:text-gray-100">
                      {priceList.name}
                    </h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {priceList.description || 'No description'}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-4">
                  {getStatusBadge()}
                  {getTypeBadge()}
                  {priceList.moneyAmounts && (
                    <span className="hidden sm:inline-flex text-sm text-gray-500">
                      {priceList.moneyAmounts.length} prices
                    </span>
                  )}
                </div>
              </div>
            </AccordionTrigger>
            <AccordionContent className="px-6 pb-4">
              <div className="space-y-4">
                <div className="flex flex-wrap gap-4 text-sm text-gray-600 dark:text-gray-400">
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4" />
                    {priceList.startsAt && priceList.endsAt ? (
                      <span>
                        {formatDate(priceList.startsAt)} - {formatDate(priceList.endsAt)}
                      </span>
                    ) : priceList.startsAt ? (
                      <span>Starts {formatDate(priceList.startsAt)}</span>
                    ) : priceList.endsAt ? (
                      <span>Ends {formatDate(priceList.endsAt)}</span>
                    ) : (
                      <span>No date restrictions</span>
                    )}
                  </div>
                </div>

                {priceList.customerGroups && priceList.customerGroups.length > 0 && (
                  <div className="border-t pt-4">
                    <h4 className="font-medium text-sm mb-2 flex items-center gap-2">
                      <Users className="w-4 h-4" />
                      Customer Groups ({priceList.customerGroups.length})
                    </h4>
                    <div className="flex flex-wrap gap-2">
                      {priceList.customerGroups.map((group) => (
                        <Badge key={group.id} variant="secondary">
                          {group.name}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {priceList.moneyAmounts && priceList.moneyAmounts.length > 0 && (
                  <div className="border-t pt-4">
                    <h4 className="font-medium text-sm mb-2 flex items-center gap-2">
                      <Package className="w-4 h-4" />
                      Price Overrides ({priceList.moneyAmounts.length})
                    </h4>
                    <div className="space-y-2">
                      {priceList.moneyAmounts.slice(0, 5).map((price) => (
                        <div key={price.id} className="flex justify-between text-sm">
                          <div className="text-gray-600 dark:text-gray-400">
                            {price.variant?.product?.title || 'Unknown Product'} - {price.variant?.title || 'Unknown Variant'}
                          </div>
                          <span className="font-medium">
                            {formatCurrency(price.amount, price.currencyCode)}
                          </span>
                        </div>
                      ))}
                      {priceList.moneyAmounts.length > 5 && (
                        <p className="text-sm text-gray-500">
                          +{priceList.moneyAmounts.length - 5} more prices
                        </p>
                      )}
                    </div>
                  </div>
                )}

                <div className="flex justify-end pt-2">
                  <Button
                    onClick={() => setIsEditOpen(true)}
                    variant="outline"
                    size="sm"
                  >
                    Edit Price List
                  </Button>
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </div>

      <EditItemDrawer
        isOpen={isEditOpen}
        onClose={() => setIsEditOpen(false)}
        itemId={priceList.id}
        listKey="PriceList"
      />
    </>
  );
}