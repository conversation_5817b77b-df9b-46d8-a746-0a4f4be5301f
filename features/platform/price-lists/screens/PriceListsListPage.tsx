import { Suspense } from 'react';
import { PageBreadcrumbs } from '@/features/dashboard/components/PageBreadcrumbs';
import { PlatformFilterBar } from '@/features/dashboard/components/PlatformFilterBar';
import { PaginationWrapper } from '@/features/dashboard/components/PaginationWrapper';
import { PriceListDetailsComponent } from '../components/PriceListDetailsComponent';
import { StatusTabs } from '../components/StatusTabs';
import { getPriceLists, getPriceListStatusCounts } from '../actions';
import { Triangle, Square, Circle } from 'lucide-react';

interface PriceListsListPageProps {
  searchParams: {
    page?: string;
    search?: string;
    sortBy?: string;
    status?: string;
    type?: string;
  };
}

async function PriceListsContent({ searchParams }: PriceListsListPageProps) {
  const page = parseInt(searchParams.page || '1');
  const search = searchParams.search || '';
  const sortBy = searchParams.sortBy || 'createdAt_DESC';
  const status = searchParams.status || 'all';

  const filters: any = {};
  if (status !== 'all' && status !== 'expired') {
    filters.status = status;
  }
  if (searchParams.type) {
    filters.type = searchParams.type;
  }

  const [{ priceLists, totalCount }, statusCounts] = await Promise.all([
    getPriceLists({ page, search, sortBy, filters }),
    getPriceListStatusCounts()
  ]);

  const totalPages = Math.ceil(totalCount / 20);

  return (
    <>
      <div className="mb-6">
        <StatusTabs
          counts={statusCounts}
          currentStatus={status}
          onStatusChange={() => {}}
        />
      </div>

      {priceLists.length === 0 ? (
        <div className="text-center py-12">
          <div className="flex justify-center items-center mb-4 space-x-2">
            <Triangle className="w-8 h-8 text-gray-300 animate-pulse" />
            <Square className="w-8 h-8 text-gray-300 animate-pulse delay-75" />
            <Circle className="w-8 h-8 text-gray-300 animate-pulse delay-150" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
            No price lists found
          </h3>
          <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
            {search ? 'Try adjusting your search terms' : 'Create your first price list to get started'}
          </p>
        </div>
      ) : (
        <>
          <div className="grid gap-4">
            {priceLists.map((priceList) => (
              <PriceListDetailsComponent key={priceList.id} priceList={priceList} />
            ))}
          </div>

          <div className="mt-8">
            <PaginationWrapper
              currentPage={page}
              totalPages={totalPages}
              basePath="/platform/price-lists"
            />
          </div>
        </>
      )}
    </>
  );
}

export default async function PriceListsListPage({ searchParams }: PriceListsListPageProps) {
  return (
    <div className="container mx-auto py-8">
      <PageBreadcrumbs
        items={[
          { label: 'Dashboard', href: '/dashboard' },
          { label: 'Platform', href: '/dashboard/platform' },
          { label: 'Price Lists' }
        ]}
      />

      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
          Price Lists
        </h1>
        <p className="mt-2 text-gray-600 dark:text-gray-400">
          Configure pricing strategies and customer-specific price tiers.
        </p>
      </div>

      <div className="mb-6">
        <PlatformFilterBar
          searchPlaceholder="Search price lists by name or description..."
          sortOptions={[
            { label: 'Newest First', value: 'createdAt_DESC' },
            { label: 'Oldest First', value: 'createdAt_ASC' },
            { label: 'Name (A-Z)', value: 'name_ASC' },
            { label: 'Name (Z-A)', value: 'name_DESC' }
          ]}
        />
      </div>

      <Suspense fallback={<div>Loading price lists...</div>}>
        <PriceListsContent searchParams={searchParams} />
      </Suspense>
    </div>
  );
}