'use server';

import { keystoneClient } from '@/features/dashboard/lib/keystoneClient';
import { UserFilters } from '../lib/types';

export async function getUsers({
  page = 1,
  perPage = 20,
  search = '',
  sortBy = 'createdAt_DESC',
  filters = {}
}: {
  page?: number;
  perPage?: number;
  search?: string;
  sortBy?: string;
  filters?: UserFilters;
}) {
  const where: any = {};
  
  if (search) {
    where.OR = [
      { name: { contains: search, mode: 'insensitive' } },
      { email: { contains: search, mode: 'insensitive' } },
      { phone: { contains: search, mode: 'insensitive' } }
    ];
  }
  
  if (filters.role) {
    where.role = { id: { equals: filters.role } };
  }
  
  if (filters.team) {
    where.team = { id: { equals: filters.team } };
  }
  
  if (filters.hasAccount !== undefined) {
    where.hasAccount = { equals: filters.hasAccount };
  }
  
  if (filters.onboardingStatus) {
    where.onboardingStatus = { equals: filters.onboardingStatus };
  }
  
  const query = `
    query UsersQuery($where: UserWhereInput, $orderBy: [UserOrderByInput!], $take: Int, $skip: Int) {
      users(where: $where, orderBy: $orderBy, take: $take, skip: $skip) {
        id
        name
        email
        phone
        hasAccount
        onboardingStatus
        firstName
        lastName
        role {
          id
          name
        }
        team {
          id
          name
        }
        addresses {
          id
          line1
          line2
          city
          provinceCode
          postalCode
          countryCode
        }
        orders {
          id
          orderNumber
          total
          createdAt
        }
        customerGroups {
          id
          name
        }
        createdAt
        updatedAt
      }
      usersCount(where: $where)
    }
  `;

  const response = await keystoneClient(query, {
    where,
    orderBy: [sortBy],
    take: perPage,
    skip: (page - 1) * perPage
  });
  
  if (!response.success) {
    throw new Error(response.error);
  }
  
  return {
    users: response.data.users || [],
    totalCount: response.data.usersCount || 0
  };
}

export async function getUserStatusCounts() {
  const query = `
    query UserStatusCounts {
      all: usersCount
      active: usersCount(where: { hasAccount: { equals: true } })
      inactive: usersCount(where: { hasAccount: { equals: false } })
      onboarding: usersCount(where: { onboardingStatus: { in: ["in_progress", "not_started"] } })
    }
  `;
  
  const response = await keystoneClient(query);
  
  if (!response.success) {
    throw new Error(response.error);
  }
  
  return {
    all: response.data.all || 0,
    active: response.data.active || 0,
    inactive: response.data.inactive || 0,
    onboarding: response.data.onboarding || 0
  };
}

export async function getRoles() {
  const query = `
    query GetRoles {
      roles {
        id
        name
      }
    }
  `;
  
  const response = await keystoneClient(query);
  
  if (!response.success) {
    throw new Error(response.error);
  }
  
  return response.data.roles || [];
}

export async function getTeams() {
  const query = `
    query GetTeams {
      teams {
        id
        name
      }
    }
  `;
  
  const response = await keystoneClient(query);
  
  if (!response.success) {
    throw new Error(response.error);
  }
  
  return response.data.teams || [];
}